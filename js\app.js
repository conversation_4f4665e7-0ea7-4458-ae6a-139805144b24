/**
 * ===================================================================
 * MAIN APPLICATION MODULE
 * Entry point and application orchestration
 * ===================================================================
 */

import { themeManager } from './modules/theme-manager.js';
import { dataManager } from './modules/data-manager.js';
import { ChartManager } from './modules/chart-manager.js';
import { UIComponents } from './modules/ui-components.js';

/**
 * Main Application Class
 * Orchestrates all modules and manages application lifecycle
 */
class DashboardApp {
    constructor() {
        this.themeManager = themeManager;
        this.dataManager = dataManager;
        this.chartManager = null;
        this.uiComponents = null;
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Initializing Financial Dashboard...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // Initialize modules in order
            await this.initializeModules();
            
            // Setup inter-module communication
            this.setupModuleCommunication();
            
            // Initialize charts and UI
            await this.initializeChartsAndUI();
            
            // Setup global error handling
            this.setupErrorHandling();
            
            this.isInitialized = true;
            console.log('✅ Dashboard initialized successfully');
            
            // Dispatch ready event
            document.dispatchEvent(new CustomEvent('dashboardReady', {
                detail: { app: this }
            }));
            
        } catch (error) {
            console.error('❌ Failed to initialize dashboard:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Initialize all modules
     */
    async initializeModules() {
        console.log('📦 Initializing modules...');
        
        // Theme manager is already initialized as singleton
        console.log('  ✓ Theme Manager');
        
        // Data manager is already initialized as singleton
        console.log('  ✓ Data Manager');
        
        // Initialize chart manager
        this.chartManager = new ChartManager(this.dataManager, this.themeManager);
        console.log('  ✓ Chart Manager');
        
        // Initialize UI components
        this.uiComponents = new UIComponents(this.dataManager, this.themeManager);
        console.log('  ✓ UI Components');
    }

    /**
     * Setup communication between modules
     */
    setupModuleCommunication() {
        console.log('🔗 Setting up module communication...');
        
        // Chart-specific events
        document.addEventListener('trendMetricChange', (e) => {
            this.chartManager.setTrendMetric(e.detail.metric);
        });

        document.addEventListener('movingAverageChange', (e) => {
            this.chartManager.setMovingAverageWindow(e.detail.window);
        });

        document.addEventListener('clearPinnedTooltips', () => {
            this.chartManager.clearPinnedTooltips();
        });

        // Theme change events are already handled by individual modules
        
        console.log('  ✓ Module communication established');
    }

    /**
     * Initialize charts and UI elements
     */
    async initializeChartsAndUI() {
        console.log('📊 Initializing charts and UI...');
        
        const activeData = this.dataManager.getActiveData();
        const yearlyData = this.dataManager.calculateYearlyAverages();
        
        // Create main charts
        this.chartManager.createTrendChart('trendChart', activeData);
        this.chartManager.createYearlyChart('yearlyChart', yearlyData);
        
        // Create mini charts
        const wasteData = activeData.map(d => d.wasteHUF);
        const marginData = activeData.map(d => d.marginPercent);
        const wastePctData = activeData.map(d => d.wastePercent);
        
        this.chartManager.createMiniChart('miniWasteChart', wasteData, 'bar');
        this.chartManager.createMiniChart('miniMarginChart', marginData, 'line');
        this.chartManager.createMiniChart('miniWastePctChart', wastePctData, 'line');
        
        // Set initial trend metric
        this.chartManager.setTrendMetric('waste');
        
        // Update mini chart badges
        this.updateMiniBadges(activeData);
        
        console.log('  ✓ Charts initialized');
        console.log('  ✓ UI components initialized');
    }

    /**
     * Update mini chart value badges
     */
    updateMiniBadges(data) {
        if (!data.length) return;
        
        const last = data[data.length - 1];
        
        const wasteEl = document.getElementById('miniWasteValue');
        const marginEl = document.getElementById('miniMarginValue');
        const wastePctEl = document.getElementById('miniWastePctValue');
        
        if (wasteEl) wasteEl.textContent = this.dataManager.formatHUF(last.wasteHUF);
        if (marginEl) marginEl.textContent = last.marginPercent.toFixed(1) + '%';
        if (wastePctEl) wastePctEl.textContent = last.wastePercent.toFixed(2) + '%';
    }

    /**
     * Setup global error handling
     */
    setupErrorHandling() {
        // Handle uncaught errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.showErrorNotification('Váratlan hiba történt. Kérjük, frissítse az oldalt.');
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.showErrorNotification('Hálózati hiba történt. Kérjük, ellenőrizze a kapcsolatot.');
        });

        // Handle Chart.js errors
        Chart.defaults.onError = (error) => {
            console.error('Chart.js error:', error);
            this.showErrorNotification('Grafikon hiba történt.');
        };
    }

    /**
     * Handle initialization errors
     */
    handleInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'error-container';
        errorContainer.innerHTML = `
            <div class="error-card">
                <h2>🚨 Inicializálási Hiba</h2>
                <p>A dashboard betöltése során hiba történt:</p>
                <pre>${error.message}</pre>
                <button onclick="location.reload()" class="btn btn-primary">
                    Oldal Újratöltése
                </button>
            </div>
        `;
        
        document.body.appendChild(errorContainer);
    }

    /**
     * Show error notification
     */
    showErrorNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">⚠️</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close" aria-label="Bezárás">×</button>
            </div>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-error-600);
            color: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        // Add to DOM
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);

        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }

    /**
     * Get application state
     */
    getState() {
        return {
            isInitialized: this.isInitialized,
            theme: this.themeManager.getCurrentState(),
            data: {
                totalRecords: this.dataManager.getAllData().length,
                activeRecords: this.dataManager.getActiveData().length,
                currentFilter: this.dataManager.getCurrentFilter()
            },
            charts: Array.from(this.chartManager.charts.keys())
        };
    }

    /**
     * Export application data
     */
    exportData(format = 'json') {
        return this.dataManager.exportData(format);
    }

    /**
     * Export application settings
     */
    exportSettings() {
        return {
            theme: this.themeManager.exportSettings(),
            timestamp: new Date().toISOString(),
            version: '2.0.0'
        };
    }

    /**
     * Import application settings
     */
    importSettings(settings) {
        try {
            if (settings.theme) {
                this.themeManager.importSettings(settings.theme);
            }
            return true;
        } catch (error) {
            console.error('Failed to import settings:', error);
            return false;
        }
    }

    /**
     * Refresh all data and charts
     */
    refresh() {
        console.log('🔄 Refreshing dashboard...');
        
        // Refresh data
        this.dataManager.applyCurrentFilter();
        
        // Update charts will be handled by data change event
        
        console.log('✅ Dashboard refreshed');
    }

    /**
     * Destroy the application
     */
    destroy() {
        console.log('🗑️ Destroying dashboard...');
        
        // Destroy charts
        if (this.chartManager) {
            this.chartManager.destroyAllCharts();
        }
        
        // Remove event listeners
        document.removeEventListener('trendMetricChange', this.handleTrendMetricChange);
        document.removeEventListener('movingAverageChange', this.handleMovingAverageChange);
        document.removeEventListener('clearPinnedTooltips', this.handleClearPinnedTooltips);
        
        this.isInitialized = false;
        console.log('✅ Dashboard destroyed');
    }

    /**
     * Get version information
     */
    getVersion() {
        return {
            version: '2.0.0',
            buildDate: new Date().toISOString(),
            modules: {
                themeManager: '2.0.0',
                dataManager: '2.0.0',
                chartManager: '2.0.0',
                uiComponents: '2.0.0'
            }
        };
    }
}

// Initialize the application
const app = new DashboardApp();

// Export for global access
window.DashboardApp = app;

// Add CSS for error notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .error-container {
        position: fixed;
        inset: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
    }
    
    .error-card {
        background: var(--card-bg-solid);
        border: 1px solid var(--card-border);
        border-radius: var(--radius-2xl);
        padding: var(--space-2xl);
        max-width: 500px;
        text-align: center;
    }
    
    .error-card h2 {
        color: var(--color-error-500);
        margin-bottom: var(--space-lg);
    }
    
    .error-card pre {
        background: var(--color-neutral-800);
        padding: var(--space-md);
        border-radius: var(--radius-lg);
        margin: var(--space-lg) 0;
        font-size: var(--font-size-sm);
        overflow-x: auto;
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        font-size: var(--font-size-lg);
        cursor: pointer;
        margin-left: auto;
    }
`;
document.head.appendChild(style);

export default app;
