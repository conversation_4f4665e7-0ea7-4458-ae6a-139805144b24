# 📊 Financial Dashboard v2.0

A comprehensive, modular financial dashboard for waste and margin analysis with advanced theming capabilities.

## 🚀 Features

### 📈 Data Visualization
- **Interactive Charts**: Trend analysis, yearly comparisons, and mini charts
- **Real-time Updates**: Dynamic chart updates based on data filters
- **Pinned Tooltips**: Click to pin chart tooltips for detailed analysis
- **Moving Averages**: Configurable moving average overlays

### 🎨 Advanced Theming
- **Multiple Color Palettes**: 15+ predefined themes across 5 categories
- **Custom Color Creation**: Real-time color customization
- **Theme Persistence**: Settings saved across sessions
- **Accessibility Support**: High contrast and accessible color schemes
- **Facts Mode**: Nutrition label-style table formatting

### 📊 Data Management
- **Flexible Filtering**: Date range and preset filters
- **Search Functionality**: Real-time data search
- **Export Options**: JSON and CSV export capabilities
- **Data Persistence**: Filter settings saved locally

### 📱 Responsive Design
- **Mobile-First**: Optimized for all screen sizes
- **Touch-Friendly**: Mobile-optimized interactions
- **Adaptive Layouts**: Dynamic grid systems

## 🏗️ Architecture

### Modular Structure
```
├── index.html              # Main HTML file
├── styles/                 # CSS Modules
│   ├── variables.css       # CSS Custom Properties
│   ├── base.css           # Base styles and typography
│   ├── components.css     # Reusable UI components
│   ├── themes.css         # Theme variations
│   ├── responsive.css     # Responsive design
│   └── utilities.css      # Utility classes
├── js/                    # JavaScript Modules
│   ├── app.js            # Main application entry point
│   └── modules/          # Feature modules
│       ├── theme-manager.js    # Theme management
│       ├── data-manager.js     # Data processing
│       ├── chart-manager.js    # Chart handling
│       └── ui-components.js    # UI components
├── config/               # Configuration files
│   └── color-palettes.js # Color palette definitions
└── docs/                # Documentation
    ├── API.md           # API documentation
    ├── THEMES.md        # Theming guide
    └── CUSTOMIZATION.md # Customization guide
```

## 🎯 Quick Start

### 1. Basic Setup
```html
<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Dashboard</title>
    
    <!-- Chart.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    
    <!-- CSS Modules -->
    <link rel="stylesheet" href="styles/variables.css">
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="styles/utilities.css">
</head>
<body>
    <!-- Your content here -->
    
    <!-- JavaScript -->
    <script type="module" src="js/app.js"></script>
</body>
</html>
```

### 2. Initialize the Dashboard
The dashboard automatically initializes when the page loads. You can access the main application instance:

```javascript
// Wait for dashboard to be ready
document.addEventListener('dashboardReady', (event) => {
    const app = event.detail.app;
    console.log('Dashboard ready!', app.getState());
});

// Or access the global instance
const app = window.DashboardApp;
```

## 🎨 Theming System

### Predefined Themes

#### Professional Themes
- **Corporate Blue**: Professional corporate theme
- **Executive Gray**: Sophisticated grayscale theme
- **Financial Green**: Finance-focused green and gold

#### Modern Themes
- **Aurora Borealis**: Northern lights inspired
- **Cyberpunk Neon**: Futuristic high contrast
- **Synthwave Retro**: 80s-inspired retro

#### Nature Themes
- **Deep Forest**: Calming forest greens
- **Ocean Depths**: Deep ocean blues
- **Sunset Calm**: Warm sunset colors

#### Accessibility Themes
- **Monochrome**: High contrast black and white
- **High Contrast**: Maximum contrast for accessibility

#### Seasonal Themes
- **Spring Bloom**: Fresh spring colors
- **Autumn Leaves**: Warm autumn tones
- **Winter Frost**: Cool winter blues

### Custom Theme Creation
```javascript
import { themeManager } from './js/modules/theme-manager.js';

// Create a custom theme
const customTheme = themeManager.createCustomTheme('My Theme', {
    accentStart: '#ff6b35',
    accentEnd: '#f7931e',
    accentAlt: '#ffb347',
    bgStart: '#2c3e50',
    bgEnd: '#34495e',
    cardBg: 'rgba(52, 73, 94, 0.85)',
    positive: '#27ae60',
    negative: '#e74c3c'
});

// Apply the theme
themeManager.switchTheme(customTheme.id);
```

## 📊 Data Management

### Data Structure
```javascript
const dataPoint = {
    date: Date,           // Date object
    dateString: String,   // Formatted date string
    wasteHUF: Number,     // Waste amount in HUF
    marginPercent: Number, // Margin percentage
    wastePercent: Number, // Waste percentage
    year: Number,         // Year
    month: Number,        // Month (1-12)
    quarter: Number       // Quarter (1-4)
};
```

### Filtering Data
```javascript
import { dataManager } from './js/modules/data-manager.js';

// Apply date range filter
dataManager.setDateFilter(
    new Date('2024-01-01'),
    new Date('2024-12-31')
);

// Apply preset filter
dataManager.applyPresetFilter('ytd'); // 'ytd', 'last3', 'last6', 'last12', 'full'

// Clear filter
dataManager.clearFilter();

// Search data
const results = dataManager.searchData('2024');
```

### Export Data
```javascript
// Export as JSON
const jsonData = dataManager.exportData('json');

// Export as CSV
const csvData = dataManager.exportData('csv');
```

## 📈 Chart Management

### Chart Types
- **Trend Chart**: Line chart for time series analysis
- **Yearly Chart**: Combined bar and line chart for yearly comparisons
- **Mini Charts**: Small charts for quick insights

### Chart Customization
```javascript
import { ChartManager } from './js/modules/chart-manager.js';

const chartManager = new ChartManager(dataManager, themeManager);

// Change trend metric
chartManager.setTrendMetric('waste'); // 'waste', 'margin', 'wastePct'

// Set moving average
chartManager.setMovingAverageWindow(6); // 6-month moving average

// Add pinned tooltip
chartManager.addPinnedTooltip(chart, point);

// Clear all pinned tooltips
chartManager.clearPinnedTooltips();
```

## 🎛️ UI Components

### Panel Management
```javascript
import { UIComponents, Panel } from './js/modules/ui-components.js';

// Create a custom panel
const panel = new Panel('myPanel', 'myToggleButton');

// Open/close panel
panel.open();
panel.close();
panel.toggle();
```

### Component Events
```javascript
// Listen for theme changes
document.addEventListener('themechange', (event) => {
    console.log('Theme changed:', event.detail);
});

// Listen for data changes
dataManager.addEventListener('dataChanged', (data) => {
    console.log('Data updated:', data);
});
```

## 🔧 Configuration

### CSS Custom Properties
The theming system is built on CSS custom properties. Key variables include:

```css
:root {
    /* Colors */
    --accent-start: #6366f1;
    --accent-end: #8b5cf6;
    --accent-alt: #a855f7;
    
    /* Backgrounds */
    --bg-gradient-start: #0f172a;
    --bg-gradient-end: #1e293b;
    --card-bg: rgba(30, 41, 59, 0.85);
    
    /* Typography */
    --font-family-sans: 'Segoe UI', sans-serif;
    --font-size-base: 1rem;
    
    /* Spacing */
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    
    /* Shadows */
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
```

## 🚀 Performance

### Optimization Features
- **Lazy Loading**: Charts load only when needed
- **Efficient Updates**: Minimal DOM manipulation
- **Memory Management**: Proper cleanup of chart instances
- **Debounced Search**: Optimized search performance

### Browser Support
- **Modern Browsers**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **ES6 Modules**: Native module support required
- **CSS Grid**: Modern layout support required

## 🧪 Testing

### Manual Testing
1. Open `index.html` in a modern browser
2. Test theme switching functionality
3. Verify data filtering and search
4. Test responsive behavior on different screen sizes
5. Validate chart interactions and tooltips

### Automated Testing
```bash
# Run tests (if test framework is set up)
npm test

# Run linting
npm run lint

# Build for production
npm run build
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation in the `docs/` folder
- Review the code comments for implementation details

---

**Built with ❤️ for modern financial analysis**
