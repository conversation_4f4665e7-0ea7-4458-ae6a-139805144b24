/**
 * ===================================================================
 * THEME MANAGER MODULE
 * Advanced theme management with persistence and dynamic switching
 * ===================================================================
 */

import { COLOR_PALETTES, THEME_CATEGORIES, createCustomPalette } from '../../config/color-palettes.js';

/**
 * Theme Manager Class
 * Handles theme switching, persistence, and customization
 */
export class ThemeManager {
    constructor() {
        this.root = document.documentElement;
        this.currentTheme = 'corporate';
        this.currentMode = 'dark'; // 'dark' | 'light'
        this.factsMode = false;
        this.factsColorMode = false;
        this.customThemes = new Map();
        
        // Storage keys
        this.STORAGE_KEYS = {
            THEME: 'dashThemeV2',
            MODE: 'dashModeV2',
            FACTS: 'dashFactsV2',
            FACTS_COLOR: 'dashFactsColorV2',
            CUSTOM_THEMES: 'dashCustomThemesV2',
            CUSTOM_COLORS: 'dashCustomColorsV2'
        };
        
        this.init();
    }

    /**
     * Initialize theme manager
     */
    init() {
        this.loadStoredSettings();
        this.applyCurrentTheme();
        this.setupEventListeners();
        this.detectSystemPreferences();
    }

    /**
     * Load stored theme settings
     */
    loadStoredSettings() {
        try {
            // Load theme
            const storedTheme = localStorage.getItem(this.STORAGE_KEYS.THEME);
            if (storedTheme && COLOR_PALETTES[storedTheme]) {
                this.currentTheme = storedTheme;
            }

            // Load mode
            const storedMode = localStorage.getItem(this.STORAGE_KEYS.MODE);
            if (storedMode === 'light' || storedMode === 'dark') {
                this.currentMode = storedMode;
            }

            // Load facts mode
            const factsMode = localStorage.getItem(this.STORAGE_KEYS.FACTS);
            this.factsMode = factsMode === 'true';

            // Load facts color mode
            const factsColorMode = localStorage.getItem(this.STORAGE_KEYS.FACTS_COLOR);
            this.factsColorMode = factsColorMode === 'true';

            // Load custom themes
            const customThemes = localStorage.getItem(this.STORAGE_KEYS.CUSTOM_THEMES);
            if (customThemes) {
                const themes = JSON.parse(customThemes);
                themes.forEach(theme => {
                    this.customThemes.set(theme.id, theme);
                });
            }

            // Load custom colors (legacy support)
            const customColors = localStorage.getItem(this.STORAGE_KEYS.CUSTOM_COLORS);
            if (customColors) {
                const colors = JSON.parse(customColors);
                this.applyCustomColors(colors);
            }
        } catch (error) {
            console.warn('Failed to load theme settings:', error);
        }
    }

    /**
     * Save current theme settings
     */
    saveSettings() {
        try {
            localStorage.setItem(this.STORAGE_KEYS.THEME, this.currentTheme);
            localStorage.setItem(this.STORAGE_KEYS.MODE, this.currentMode);
            localStorage.setItem(this.STORAGE_KEYS.FACTS, this.factsMode.toString());
            localStorage.setItem(this.STORAGE_KEYS.FACTS_COLOR, this.factsColorMode.toString());
            
            // Save custom themes
            const customThemesArray = Array.from(this.customThemes.values());
            localStorage.setItem(this.STORAGE_KEYS.CUSTOM_THEMES, JSON.stringify(customThemesArray));
        } catch (error) {
            console.warn('Failed to save theme settings:', error);
        }
    }

    /**
     * Apply current theme to DOM
     */
    applyCurrentTheme() {
        // Remove existing theme classes
        document.body.classList.remove('light-theme', 'facts-mode', 'facts-color');
        Object.keys(THEME_CATEGORIES).forEach(category => {
            Object.keys(COLOR_PALETTES).forEach(paletteId => {
                document.body.classList.remove(`theme-${paletteId}`);
            });
        });

        // Apply current theme
        const palette = this.getCurrentPalette();
        if (palette) {
            this.applyPalette(palette);
        }

        // Apply mode classes
        if (this.currentMode === 'light') {
            document.body.classList.add('light-theme');
        }

        if (this.factsMode) {
            document.body.classList.add('facts-mode');
        }

        if (this.factsColorMode) {
            document.body.classList.add('facts-color');
        }

        // Dispatch theme change event
        this.dispatchThemeChangeEvent();
    }

    /**
     * Apply palette colors to CSS custom properties
     */
    applyPalette(palette) {
        if (!palette || !palette.vars) return;

        Object.entries(palette.vars).forEach(([property, value]) => {
            this.root.style.setProperty(property, value);
        });

        // Add theme class for additional styling
        document.body.classList.add(`theme-${palette.id}`);
    }

    /**
     * Apply custom colors directly
     */
    applyCustomColors(colors) {
        Object.entries(colors).forEach(([property, value]) => {
            this.root.style.setProperty(property, value);
        });
    }

    /**
     * Get current palette object
     */
    getCurrentPalette() {
        return COLOR_PALETTES[this.currentTheme] || 
               this.customThemes.get(this.currentTheme) || 
               COLOR_PALETTES.corporate;
    }

    /**
     * Switch to a different theme
     */
    switchTheme(themeId) {
        if (COLOR_PALETTES[themeId] || this.customThemes.has(themeId)) {
            this.currentTheme = themeId;
            this.applyCurrentTheme();
            this.saveSettings();
            return true;
        }
        return false;
    }

    /**
     * Toggle between light and dark mode
     */
    toggleMode() {
        this.currentMode = this.currentMode === 'dark' ? 'light' : 'dark';
        this.applyCurrentTheme();
        this.saveSettings();
        return this.currentMode;
    }

    /**
     * Toggle facts mode
     */
    toggleFactsMode() {
        this.factsMode = !this.factsMode;
        this.applyCurrentTheme();
        this.saveSettings();
        return this.factsMode;
    }

    /**
     * Toggle facts color mode
     */
    toggleFactsColorMode() {
        this.factsColorMode = !this.factsColorMode;
        this.applyCurrentTheme();
        this.saveSettings();
        return this.factsColorMode;
    }

    /**
     * Create and save a custom theme
     */
    createCustomTheme(name, colors) {
        const customTheme = createCustomPalette(name, colors);
        this.customThemes.set(customTheme.id, customTheme);
        this.saveSettings();
        return customTheme;
    }

    /**
     * Delete a custom theme
     */
    deleteCustomTheme(themeId) {
        if (this.customThemes.has(themeId)) {
            this.customThemes.delete(themeId);
            this.saveSettings();
            
            // Switch to default theme if current theme was deleted
            if (this.currentTheme === themeId) {
                this.switchTheme('corporate');
            }
            return true;
        }
        return false;
    }

    /**
     * Get all available themes (built-in + custom)
     */
    getAllThemes() {
        const builtInThemes = Object.values(COLOR_PALETTES);
        const customThemes = Array.from(this.customThemes.values());
        return [...builtInThemes, ...customThemes];
    }

    /**
     * Get themes by category
     */
    getThemesByCategory(category) {
        return this.getAllThemes().filter(theme => theme.category === category);
    }

    /**
     * Reset to default theme
     */
    resetToDefault() {
        this.currentTheme = 'corporate';
        this.currentMode = 'dark';
        this.factsMode = false;
        this.factsColorMode = false;
        this.applyCurrentTheme();
        this.saveSettings();
    }

    /**
     * Detect system color scheme preference
     */
    detectSystemPreferences() {
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const lightModeQuery = window.matchMedia('(prefers-color-scheme: light)');
            
            // Only apply system preference if no stored preference exists
            if (!localStorage.getItem(this.STORAGE_KEYS.MODE)) {
                if (darkModeQuery.matches) {
                    this.currentMode = 'dark';
                } else if (lightModeQuery.matches) {
                    this.currentMode = 'light';
                }
                this.applyCurrentTheme();
            }

            // Listen for system preference changes
            darkModeQuery.addEventListener('change', (e) => {
                if (e.matches && !localStorage.getItem(this.STORAGE_KEYS.MODE)) {
                    this.currentMode = 'dark';
                    this.applyCurrentTheme();
                }
            });

            lightModeQuery.addEventListener('change', (e) => {
                if (e.matches && !localStorage.getItem(this.STORAGE_KEYS.MODE)) {
                    this.currentMode = 'light';
                    this.applyCurrentTheme();
                }
            });
        }
    }

    /**
     * Setup event listeners for theme changes
     */
    setupEventListeners() {
        // Listen for storage changes from other tabs
        window.addEventListener('storage', (e) => {
            if (Object.values(this.STORAGE_KEYS).includes(e.key)) {
                this.loadStoredSettings();
                this.applyCurrentTheme();
            }
        });
    }

    /**
     * Dispatch custom theme change event
     */
    dispatchThemeChangeEvent() {
        const event = new CustomEvent('themechange', {
            detail: {
                theme: this.currentTheme,
                mode: this.currentMode,
                factsMode: this.factsMode,
                factsColorMode: this.factsColorMode,
                palette: this.getCurrentPalette()
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Get current theme state
     */
    getCurrentState() {
        return {
            theme: this.currentTheme,
            mode: this.currentMode,
            factsMode: this.factsMode,
            factsColorMode: this.factsColorMode,
            palette: this.getCurrentPalette()
        };
    }

    /**
     * Export current theme settings
     */
    exportSettings() {
        return {
            theme: this.currentTheme,
            mode: this.currentMode,
            factsMode: this.factsMode,
            factsColorMode: this.factsColorMode,
            customThemes: Array.from(this.customThemes.values())
        };
    }

    /**
     * Import theme settings
     */
    importSettings(settings) {
        try {
            if (settings.theme) this.currentTheme = settings.theme;
            if (settings.mode) this.currentMode = settings.mode;
            if (typeof settings.factsMode === 'boolean') this.factsMode = settings.factsMode;
            if (typeof settings.factsColorMode === 'boolean') this.factsColorMode = settings.factsColorMode;
            
            if (settings.customThemes && Array.isArray(settings.customThemes)) {
                this.customThemes.clear();
                settings.customThemes.forEach(theme => {
                    this.customThemes.set(theme.id, theme);
                });
            }

            this.applyCurrentTheme();
            this.saveSettings();
            return true;
        } catch (error) {
            console.error('Failed to import theme settings:', error);
            return false;
        }
    }
}

// Create and export singleton instance
export const themeManager = new ThemeManager();
