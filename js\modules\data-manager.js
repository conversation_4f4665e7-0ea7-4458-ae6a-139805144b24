/**
 * ===================================================================
 * DATA MANAGER MODULE
 * Handles data processing, filtering, and state management
 * ===================================================================
 */

/**
 * Data Manager Class
 * Manages financial data, filtering, and calculations
 */
export class DataManager {
    constructor() {
        this.rawData = [];
        this.processedData = [];
        this.activeData = [];
        this.currentFilter = null;
        this.listeners = new Map();
        
        // Storage key for date filters
        this.DATE_FILTER_KEY = 'dashDateFilterV2';
        
        this.init();
    }

    /**
     * Initialize data manager
     */
    init() {
        this.loadRawData();
        this.processData();
        this.loadStoredFilter();
        this.applyCurrentFilter();
    }

    /**
     * Load raw data (in real app, this would come from API)
     */
    loadRawData() {
        this.rawData = [
            ["2022-12-31T23:00:00.000Z", 429566, 0.321, 0.028],
            ["2023-01-31T23:00:00.000Z", 211051, 0.32, 0.014],
            ["2023-02-28T23:00:00.000Z", 300051, 0.273, 0.017],
            ["2023-03-31T22:00:00.000Z", 349641, 0.29, 0.024],
            ["2023-04-30T22:00:00.000Z", 343470, 0.298, 0.02],
            ["2023-05-31T22:00:00.000Z", 421373, 0.288, 0.028],
            ["2023-06-30T22:00:00.000Z", 390375, 0.276, 0.03],
            ["2023-07-31T22:00:00.000Z", 375140, 0.24, 0.026],
            ["2023-08-31T22:00:00.000Z", 284146, 0.298, 0.017],
            ["2023-09-30T22:00:00.000Z", 278952, 0.259, 0.017],
            ["2023-10-31T23:00:00.000Z", 132999, 0.35, 0.008],
            ["2023-11-30T23:00:00.000Z", 196828, 0.318, 0.014],
            ["2023-12-31T23:00:00.000Z", 150022, 0.272, 0.01],
            ["2024-01-31T23:00:00.000Z", 171387, 0.322, 0.01],
            ["2024-02-29T23:00:00.000Z", 203969, 0.316, 0.012],
            ["2024-03-31T22:00:00.000Z", 156747, 0.205, 0.01],
            ["2024-04-30T22:00:00.000Z", 196673, 0.191, 0.011],
            ["2024-05-31T22:00:00.000Z", 168188, 0.28, 0.012],
            ["2024-06-30T22:00:00.000Z", 139796, 0.246, 0.01],
            ["2024-07-31T22:00:00.000Z", 147551, 0.234, 0.011],
            ["2024-08-31T22:00:00.000Z", 183731, 0.292, 0.011],
            ["2024-09-30T22:00:00.000Z", 203242, 0.254, 0.011],
            ["2024-10-31T23:00:00.000Z", 309666, 0.299, 0.019],
            ["2024-11-30T23:00:00.000Z", 414283, 0.277, 0.029],
            ["2024-12-31T23:00:00.000Z", 296550, 0.279, 0.019],
            ["2025-01-31T23:00:00.000Z", 352828, 0.308, 0.022],
            ["2025-02-28T23:00:00.000Z", 446357, 0.303, 0.025],
            ["2025-03-31T22:00:00.000Z", 584288, 0.313, 0.033],
            ["2025-04-30T22:00:00.000Z", 471640, 0.263, 0.024],
            ["2025-05-31T22:00:00.000Z", 542692, 0.307, 0.037],
            ["2025-06-30T22:00:00.000Z", 518160, 0.307, 0.034],
            ["2025-07-31T22:00:00.000Z", 384280, 0.278, 0.027]
        ];
    }

    /**
     * Process raw data into structured format
     */
    processData() {
        this.processedData = this.rawData.map(row => ({
            date: new Date(row[0]),
            dateString: new Date(row[0]).toLocaleDateString('hu-HU', { 
                year: 'numeric', 
                month: 'long' 
            }),
            wasteHUF: row[1],
            marginPercent: row[2] * 100,
            wastePercent: row[3] * 100,
            // Additional calculated fields
            year: new Date(row[0]).getFullYear(),
            month: new Date(row[0]).getMonth() + 1,
            quarter: Math.ceil((new Date(row[0]).getMonth() + 1) / 3)
        }));
    }

    /**
     * Apply current filter to data
     */
    applyCurrentFilter() {
        if (!this.currentFilter) {
            this.activeData = [...this.processedData];
        } else {
            this.activeData = this.processedData.filter(row => {
                const time = row.date.getTime();
                if (this.currentFilter.from && time < this.currentFilter.from.getTime()) return false;
                if (this.currentFilter.to && time > this.currentFilter.to.getTime()) return false;
                return true;
            });
        }

        this.saveCurrentFilter();
        this.notifyListeners('dataChanged', {
            activeData: this.activeData,
            filter: this.currentFilter
        });
    }

    /**
     * Set date range filter
     */
    setDateFilter(from, to, preset = null) {
        this.currentFilter = {
            from: from ? new Date(from) : null,
            to: to ? new Date(to) : null,
            preset: preset
        };
        this.applyCurrentFilter();
    }

    /**
     * Apply preset filter
     */
    applyPresetFilter(preset) {
        const sorted = [...this.processedData].sort((a, b) => a.date - b.date);
        const last = sorted[sorted.length - 1];
        let from = null, to = null;

        switch (preset) {
            case 'full':
                from = sorted[0].date;
                to = last.date;
                break;
            case 'ytd':
                from = new Date(last.date.getFullYear(), 0, 1);
                to = last.date;
                break;
            case 'last3':
            case 'last6':
            case 'last12':
                const months = parseInt(preset.replace('last', ''));
                to = last.date;
                const fromIndex = Math.max(0, sorted.length - months);
                from = sorted[fromIndex].date;
                break;
            default:
                this.clearFilter();
                return;
        }

        this.setDateFilter(from, to, preset);
    }

    /**
     * Clear current filter
     */
    clearFilter() {
        this.currentFilter = null;
        this.applyCurrentFilter();
    }

    /**
     * Get filtered data
     */
    getActiveData() {
        return this.activeData;
    }

    /**
     * Get all processed data
     */
    getAllData() {
        return this.processedData;
    }

    /**
     * Calculate summary statistics
     */
    calculateStats() {
        const data = this.activeData;
        if (!data.length) {
            return {
                totalWaste: 0,
                avgMargin: 0,
                avgWastePercent: 0,
                monthCount: 0
            };
        }

        return {
            totalWaste: data.reduce((sum, row) => sum + row.wasteHUF, 0),
            avgMargin: data.reduce((sum, row) => sum + row.marginPercent, 0) / data.length,
            avgWastePercent: data.reduce((sum, row) => sum + row.wastePercent, 0) / data.length,
            monthCount: data.length
        };
    }

    /**
     * Calculate yearly averages
     */
    calculateYearlyAverages() {
        const yearlyData = {};
        
        this.activeData.forEach(row => {
            const year = row.year;
            if (!yearlyData[year]) {
                yearlyData[year] = { 
                    wasteSum: 0, 
                    marginSum: 0, 
                    wastePercentSum: 0, 
                    count: 0,
                    months: []
                };
            }
            yearlyData[year].wasteSum += row.wasteHUF;
            yearlyData[year].marginSum += row.marginPercent;
            yearlyData[year].wastePercentSum += row.wastePercent;
            yearlyData[year].count++;
            yearlyData[year].months.push(row);
        });

        Object.keys(yearlyData).forEach(year => {
            const data = yearlyData[year];
            yearlyData[year] = {
                ...data,
                avgWaste: data.wasteSum / data.count,
                avgMargin: data.marginSum / data.count,
                avgWastePercent: data.wastePercentSum / data.count
            };
        });

        return yearlyData;
    }

    /**
     * Calculate moving average
     */
    calculateMovingAverage(data, windowSize) {
        if (windowSize <= 1) return null;
        
        const result = [];
        for (let i = 0; i < data.length; i++) {
            if (i + 1 < windowSize) {
                result.push(null);
                continue;
            }
            
            let sum = 0;
            for (let j = i - windowSize + 1; j <= i; j++) {
                sum += data[j];
            }
            result.push(sum / windowSize);
        }
        
        return result;
    }

    /**
     * Search data
     */
    searchData(searchTerm) {
        if (!searchTerm) return this.activeData;
        
        const term = searchTerm.toLowerCase();
        return this.activeData.filter(row => 
            row.dateString.toLowerCase().includes(term) ||
            row.wasteHUF.toString().includes(term) ||
            row.marginPercent.toFixed(1).includes(term) ||
            row.wastePercent.toFixed(2).includes(term)
        );
    }

    /**
     * Load stored filter from localStorage
     */
    loadStoredFilter() {
        try {
            const stored = JSON.parse(localStorage.getItem(this.DATE_FILTER_KEY) || 'null');
            if (stored) {
                this.currentFilter = {
                    from: stored.from ? new Date(stored.from) : null,
                    to: stored.to ? new Date(stored.to) : null,
                    preset: stored.preset || null
                };
            }
        } catch (error) {
            console.warn('Failed to load stored filter:', error);
        }
    }

    /**
     * Save current filter to localStorage
     */
    saveCurrentFilter() {
        try {
            if (!this.currentFilter) {
                localStorage.removeItem(this.DATE_FILTER_KEY);
                return;
            }
            
            localStorage.setItem(this.DATE_FILTER_KEY, JSON.stringify({
                from: this.currentFilter.from ? this.currentFilter.from.toISOString() : null,
                to: this.currentFilter.to ? this.currentFilter.to.toISOString() : null,
                preset: this.currentFilter.preset || null
            }));
        } catch (error) {
            console.warn('Failed to save filter:', error);
        }
    }

    /**
     * Add event listener
     */
    addEventListener(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        this.listeners.get(event).add(callback);
    }

    /**
     * Remove event listener
     */
    removeEventListener(event, callback) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).delete(callback);
        }
    }

    /**
     * Notify listeners of events
     */
    notifyListeners(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Error in data manager listener:', error);
                }
            });
        }
    }

    /**
     * Get current filter state
     */
    getCurrentFilter() {
        return this.currentFilter;
    }

    /**
     * Format currency value
     */
    formatHUF(amount) {
        return new Intl.NumberFormat('hu-HU', {
            style: 'currency',
            currency: 'HUF',
            minimumFractionDigits: 0
        }).format(amount);
    }

    /**
     * Export data as JSON
     */
    exportData(format = 'json') {
        const data = {
            metadata: {
                exportDate: new Date().toISOString(),
                totalRecords: this.activeData.length,
                filter: this.currentFilter
            },
            data: this.activeData
        };

        if (format === 'csv') {
            return this.convertToCSV(data.data);
        }
        
        return JSON.stringify(data, null, 2);
    }

    /**
     * Convert data to CSV format
     */
    convertToCSV(data) {
        if (!data.length) return '';
        
        const headers = ['Date', 'Waste HUF', 'Margin %', 'Waste %'];
        const rows = data.map(row => [
            row.dateString,
            row.wasteHUF,
            row.marginPercent.toFixed(2),
            row.wastePercent.toFixed(2)
        ]);
        
        return [headers, ...rows]
            .map(row => row.map(cell => `"${cell}"`).join(','))
            .join('\n');
    }
}

// Create and export singleton instance
export const dataManager = new DataManager();
