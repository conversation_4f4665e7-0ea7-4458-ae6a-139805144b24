# 🛠️ Customization Guide

Complete guide for customizing and extending the Financial Dashboard v2.0.

## 🎯 Overview

The dashboard is designed to be highly customizable through:
- **Modular architecture** for easy extension
- **CSS custom properties** for styling
- **Event-driven communication** between modules
- **Configuration objects** for behavior modification

## 🏗️ Architecture Customization

### Adding New Modules

#### 1. Create Module File
```javascript
// js/modules/my-custom-module.js
export class MyCustomModule {
    constructor(dataManager, themeManager) {
        this.dataManager = dataManager;
        this.themeManager = themeManager;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeFeatures();
    }

    setupEventListeners() {
        // Listen for data changes
        this.dataManager.addEventListener('dataChanged', (data) => {
            this.handleDataChange(data);
        });

        // Listen for theme changes
        document.addEventListener('themechange', (event) => {
            this.handleThemeChange(event.detail);
        });
    }

    handleDataChange(data) {
        // Handle data updates
        console.log('Data changed in custom module:', data);
    }

    handleThemeChange(themeState) {
        // Handle theme updates
        console.log('Theme changed in custom module:', themeState);
    }

    initializeFeatures() {
        // Initialize custom features
    }
}
```

#### 2. Integrate with Main App
```javascript
// js/app.js
import { MyCustomModule } from './modules/my-custom-module.js';

class DashboardApp {
    async initializeModules() {
        // ... existing modules ...
        
        // Add custom module
        this.myCustomModule = new MyCustomModule(this.dataManager, this.themeManager);
        console.log('  ✓ My Custom Module');
    }
}
```

### Extending Existing Modules

#### Data Manager Extension
```javascript
// Extend data manager with custom calculations
import { dataManager } from './js/modules/data-manager.js';

// Add custom method
dataManager.calculateCustomMetric = function() {
    const data = this.getActiveData();
    return data.reduce((sum, item) => sum + item.customValue, 0);
};

// Add custom event listener
dataManager.addEventListener('dataChanged', (data) => {
    const customMetric = dataManager.calculateCustomMetric();
    document.dispatchEvent(new CustomEvent('customMetricCalculated', {
        detail: { metric: customMetric }
    }));
});
```

#### Theme Manager Extension
```javascript
// Add custom theme category
import { COLOR_PALETTES } from '../config/color-palettes.js';

COLOR_PALETTES.myCustomTheme = {
    id: 'myCustomTheme',
    name: 'My Custom Theme',
    category: 'custom',
    description: 'My custom theme description',
    accessibility: 'AA',
    vars: {
        '--accent-start': '#custom-color-1',
        '--accent-end': '#custom-color-2',
        // ... other variables
    }
};
```

## 🎨 Visual Customization

### Custom CSS Components

#### Creating New Component Styles
```css
/* styles/custom-components.css */
.my-custom-card {
    background: linear-gradient(155deg, var(--card-bg), rgba(255,255,255,0.02));
    backdrop-filter: blur(18px) saturate(160%);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl);
    box-shadow: var(--card-shadow);
    transition: transform var(--duration-normal) var(--ease-out);
}

.my-custom-card:hover {
    transform: translateY(-4px);
}

.my-custom-button {
    background: var(--button-primary);
    color: white;
    border: none;
    border-radius: var(--radius-full);
    padding: var(--space-sm) var(--space-lg);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
}

.my-custom-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}
```

#### Adding Custom CSS Variables
```css
/* styles/custom-variables.css */
:root {
    /* Custom spacing */
    --space-custom: 2.5rem;
    
    /* Custom colors */
    --color-custom-primary: #your-color;
    --color-custom-secondary: #your-color;
    
    /* Custom animations */
    --duration-custom: 750ms;
    --ease-custom: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    
    /* Custom shadows */
    --shadow-custom: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}
```

### Responsive Customization

#### Custom Breakpoints
```css
/* styles/custom-responsive.css */
@media (min-width: 1600px) {
    .container {
        max-width: 1600px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(6, 1fr);
    }
}

@media (max-width: 480px) {
    .my-custom-card {
        padding: var(--space-md);
    }
}
```

#### Custom Grid Layouts
```css
.custom-dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    grid-template-rows: auto auto 1fr;
    gap: var(--space-xl);
    grid-template-areas:
        "main sidebar1 sidebar2"
        "charts charts charts"
        "table table table";
}

.main-content { grid-area: main; }
.sidebar-1 { grid-area: sidebar1; }
.sidebar-2 { grid-area: sidebar2; }
.charts-section { grid-area: charts; }
.table-section { grid-area: table; }
```

## 📊 Chart Customization

### Custom Chart Types

#### Adding New Chart Type
```javascript
// js/modules/custom-charts.js
export class CustomChartManager {
    createHeatmapChart(canvasId, data) {
        const canvas = document.getElementById(canvasId);
        const ctx = canvas.getContext('2d');
        
        // Custom heatmap implementation
        const chart = new Chart(ctx, {
            type: 'scatter',
            data: this.processHeatmapData(data),
            options: {
                responsive: true,
                plugins: {
                    legend: { display: false }
                },
                scales: {
                    x: { type: 'time' },
                    y: { beginAtZero: true }
                }
            }
        });
        
        return chart;
    }
    
    processHeatmapData(data) {
        // Process data for heatmap visualization
        return {
            datasets: [{
                label: 'Heatmap Data',
                data: data.map(item => ({
                    x: item.date,
                    y: item.value,
                    r: item.intensity * 10
                })),
                backgroundColor: 'rgba(255, 99, 132, 0.6)'
            }]
        };
    }
}
```

#### Custom Chart Plugins
```javascript
// Custom Chart.js plugin
const customTooltipPlugin = {
    id: 'customTooltip',
    afterDraw: (chart) => {
        // Custom tooltip rendering
        const { ctx } = chart;
        // ... custom drawing logic
    }
};

Chart.register(customTooltipPlugin);
```

### Chart Styling Customization

#### Custom Chart Colors
```javascript
// js/modules/chart-colors.js
export const CUSTOM_CHART_COLORS = {
    primary: ['#FF6384', '#36A2EB', '#FFCE56'],
    secondary: ['#4BC0C0', '#9966FF', '#FF9F40'],
    gradients: {
        blue: (ctx) => {
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, 'rgba(54, 162, 235, 0.8)');
            gradient.addColorStop(1, 'rgba(54, 162, 235, 0.1)');
            return gradient;
        }
    }
};
```

## 🔧 Data Customization

### Custom Data Sources

#### API Integration
```javascript
// js/modules/api-data-source.js
export class APIDataSource {
    constructor(apiUrl, apiKey) {
        this.apiUrl = apiUrl;
        this.apiKey = apiKey;
    }
    
    async fetchData(params = {}) {
        try {
            const response = await fetch(`${this.apiUrl}/data`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                params: new URLSearchParams(params)
            });
            
            const data = await response.json();
            return this.transformData(data);
        } catch (error) {
            console.error('Failed to fetch data:', error);
            return [];
        }
    }
    
    transformData(rawData) {
        return rawData.map(item => ({
            date: new Date(item.timestamp),
            dateString: new Date(item.timestamp).toLocaleDateString('hu-HU'),
            wasteHUF: item.waste_amount,
            marginPercent: item.margin * 100,
            wastePercent: item.waste_percentage * 100,
            year: new Date(item.timestamp).getFullYear(),
            month: new Date(item.timestamp).getMonth() + 1,
            quarter: Math.ceil((new Date(item.timestamp).getMonth() + 1) / 3)
        }));
    }
}

// Integration with data manager
import { dataManager } from './data-manager.js';

const apiSource = new APIDataSource('https://api.example.com', 'your-api-key');

// Replace loadRawData method
dataManager.loadRawData = async function() {
    this.rawData = await apiSource.fetchData();
    this.processData();
};
```

#### Custom Data Processing
```javascript
// Add custom data processing methods
dataManager.addCustomProcessor = function(name, processor) {
    this.customProcessors = this.customProcessors || {};
    this.customProcessors[name] = processor;
};

// Example custom processor
dataManager.addCustomProcessor('seasonalAdjustment', (data) => {
    return data.map(item => ({
        ...item,
        seasonallyAdjusted: item.wasteHUF * getSeasonalFactor(item.month)
    }));
});

function getSeasonalFactor(month) {
    const factors = {
        1: 1.1, 2: 1.05, 3: 1.0, 4: 0.95, 5: 0.9, 6: 0.85,
        7: 0.8, 8: 0.85, 9: 0.9, 10: 0.95, 11: 1.0, 12: 1.05
    };
    return factors[month] || 1.0;
}
```

### Custom Calculations

#### Advanced Analytics
```javascript
// js/modules/analytics.js
export class AdvancedAnalytics {
    constructor(dataManager) {
        this.dataManager = dataManager;
    }
    
    calculateTrend(data, metric) {
        // Linear regression for trend calculation
        const n = data.length;
        const sumX = data.reduce((sum, _, i) => sum + i, 0);
        const sumY = data.reduce((sum, item) => sum + item[metric], 0);
        const sumXY = data.reduce((sum, item, i) => sum + i * item[metric], 0);
        const sumXX = data.reduce((sum, _, i) => sum + i * i, 0);
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;
        
        return { slope, intercept };
    }
    
    calculateSeasonality(data, metric) {
        // Seasonal decomposition
        const monthlyAverages = {};
        data.forEach(item => {
            const month = item.month;
            if (!monthlyAverages[month]) {
                monthlyAverages[month] = { sum: 0, count: 0 };
            }
            monthlyAverages[month].sum += item[metric];
            monthlyAverages[month].count++;
        });
        
        Object.keys(monthlyAverages).forEach(month => {
            monthlyAverages[month] = monthlyAverages[month].sum / monthlyAverages[month].count;
        });
        
        return monthlyAverages;
    }
    
    detectAnomalies(data, metric, threshold = 2) {
        // Statistical anomaly detection
        const values = data.map(item => item[metric]);
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        const stdDev = Math.sqrt(variance);
        
        return data.filter(item => {
            const zScore = Math.abs((item[metric] - mean) / stdDev);
            return zScore > threshold;
        });
    }
}
```

## 🎛️ UI Customization

### Custom Components

#### Creating Custom Panels
```javascript
// js/components/custom-panel.js
import { Panel } from '../modules/ui-components.js';

export class CustomAnalyticsPanel extends Panel {
    constructor() {
        super('analyticsPanel', 'analyticsToggle');
        this.analytics = new AdvancedAnalytics(dataManager);
        this.initializeContent();
    }
    
    initializeContent() {
        const content = `
            <div class="analytics-content">
                <h3>📈 Advanced Analytics</h3>
                <div class="analytics-section">
                    <h4>Trend Analysis</h4>
                    <div id="trendResults"></div>
                </div>
                <div class="analytics-section">
                    <h4>Seasonality</h4>
                    <div id="seasonalityResults"></div>
                </div>
                <div class="analytics-section">
                    <h4>Anomalies</h4>
                    <div id="anomalyResults"></div>
                </div>
            </div>
        `;
        
        this.panel.innerHTML = content;
        this.updateAnalytics();
    }
    
    updateAnalytics() {
        const data = dataManager.getActiveData();
        
        // Update trend analysis
        const trend = this.analytics.calculateTrend(data, 'wasteHUF');
        document.getElementById('trendResults').innerHTML = 
            `Slope: ${trend.slope.toFixed(2)}, Intercept: ${trend.intercept.toFixed(2)}`;
        
        // Update seasonality
        const seasonality = this.analytics.calculateSeasonality(data, 'wasteHUF');
        document.getElementById('seasonalityResults').innerHTML = 
            Object.entries(seasonality)
                .map(([month, avg]) => `Month ${month}: ${avg.toFixed(0)}`)
                .join('<br>');
        
        // Update anomalies
        const anomalies = this.analytics.detectAnomalies(data, 'wasteHUF');
        document.getElementById('anomalyResults').innerHTML = 
            `${anomalies.length} anomalies detected`;
    }
}
```

#### Custom Form Controls
```javascript
// js/components/custom-controls.js
export class CustomDateRangePicker {
    constructor(containerId, onDateChange) {
        this.container = document.getElementById(containerId);
        this.onDateChange = onDateChange;
        this.render();
    }
    
    render() {
        this.container.innerHTML = `
            <div class="custom-date-picker">
                <div class="date-input-group">
                    <label>From:</label>
                    <input type="date" id="customFromDate" class="custom-date-input">
                </div>
                <div class="date-input-group">
                    <label>To:</label>
                    <input type="date" id="customToDate" class="custom-date-input">
                </div>
                <button type="button" class="btn btn-primary" id="applyCustomDates">
                    Apply
                </button>
            </div>
        `;
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        document.getElementById('applyCustomDates').addEventListener('click', () => {
            const fromDate = document.getElementById('customFromDate').value;
            const toDate = document.getElementById('customToDate').value;
            
            if (fromDate && toDate) {
                this.onDateChange(new Date(fromDate), new Date(toDate));
            }
        });
    }
}
```

## 🚀 Performance Optimization

### Custom Optimization Strategies

#### Lazy Loading
```javascript
// js/utils/lazy-loader.js
export class LazyLoader {
    constructor() {
        this.loadedModules = new Set();
    }
    
    async loadModule(moduleName) {
        if (this.loadedModules.has(moduleName)) {
            return;
        }
        
        try {
            const module = await import(`../modules/${moduleName}.js`);
            this.loadedModules.add(moduleName);
            return module;
        } catch (error) {
            console.error(`Failed to load module ${moduleName}:`, error);
        }
    }
    
    async loadOnDemand(trigger, moduleName) {
        document.addEventListener(trigger, async () => {
            await this.loadModule(moduleName);
        }, { once: true });
    }
}
```

#### Data Virtualization
```javascript
// js/components/virtual-table.js
export class VirtualTable {
    constructor(containerId, data, rowHeight = 40) {
        this.container = document.getElementById(containerId);
        this.data = data;
        this.rowHeight = rowHeight;
        this.visibleRows = Math.ceil(this.container.clientHeight / rowHeight);
        this.scrollTop = 0;
        
        this.render();
        this.setupScrollListener();
    }
    
    render() {
        const startIndex = Math.floor(this.scrollTop / this.rowHeight);
        const endIndex = Math.min(startIndex + this.visibleRows, this.data.length);
        
        const visibleData = this.data.slice(startIndex, endIndex);
        
        this.container.innerHTML = `
            <div class="virtual-table-wrapper" style="height: ${this.data.length * this.rowHeight}px;">
                <div class="virtual-table-content" style="transform: translateY(${startIndex * this.rowHeight}px);">
                    ${visibleData.map(row => this.renderRow(row)).join('')}
                </div>
            </div>
        `;
    }
    
    renderRow(rowData) {
        return `
            <div class="virtual-table-row" style="height: ${this.rowHeight}px;">
                ${Object.values(rowData).map(cell => `<span>${cell}</span>`).join('')}
            </div>
        `;
    }
    
    setupScrollListener() {
        this.container.addEventListener('scroll', () => {
            this.scrollTop = this.container.scrollTop;
            this.render();
        });
    }
}
```

## 🔍 Debugging and Development

### Custom Development Tools

#### Debug Panel
```javascript
// js/utils/debug-panel.js
export class DebugPanel {
    constructor() {
        this.isVisible = false;
        this.createPanel();
        this.setupKeyboardShortcut();
    }
    
    createPanel() {
        const panel = document.createElement('div');
        panel.id = 'debugPanel';
        panel.className = 'debug-panel';
        panel.innerHTML = `
            <h3>🐛 Debug Panel</h3>
            <div class="debug-section">
                <h4>Application State</h4>
                <pre id="appState"></pre>
            </div>
            <div class="debug-section">
                <h4>Performance Metrics</h4>
                <pre id="perfMetrics"></pre>
            </div>
            <button id="refreshDebug">Refresh</button>
        `;
        
        document.body.appendChild(panel);
        this.updateDebugInfo();
    }
    
    setupKeyboardShortcut() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                this.toggle();
            }
        });
    }
    
    toggle() {
        this.isVisible = !this.isVisible;
        const panel = document.getElementById('debugPanel');
        panel.style.display = this.isVisible ? 'block' : 'none';
        
        if (this.isVisible) {
            this.updateDebugInfo();
        }
    }
    
    updateDebugInfo() {
        const appState = window.DashboardApp?.getState() || {};
        const perfMetrics = this.getPerformanceMetrics();
        
        document.getElementById('appState').textContent = 
            JSON.stringify(appState, null, 2);
        document.getElementById('perfMetrics').textContent = 
            JSON.stringify(perfMetrics, null, 2);
    }
    
    getPerformanceMetrics() {
        return {
            memory: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : 'Not available',
            timing: performance.timing ? {
                loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
                domReady: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart
            } : 'Not available'
        };
    }
}
```

This comprehensive customization guide provides the foundation for extending and modifying the Financial Dashboard to meet specific requirements while maintaining the modular architecture and performance characteristics.
