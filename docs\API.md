# 📚 API Documentation

Complete API reference for the Financial Dashboard v2.0.

## 🏗️ Core Modules

### DashboardApp

Main application class that orchestrates all modules.

#### Constructor
```javascript
const app = new DashboardApp();
```

#### Methods

##### `getState()`
Returns the current application state.

```javascript
const state = app.getState();
// Returns:
// {
//   isInitialized: boolean,
//   theme: object,
//   data: object,
//   charts: string[]
// }
```

##### `exportData(format)`
Exports application data in specified format.

```javascript
const jsonData = app.exportData('json');
const csvData = app.exportData('csv');
```

**Parameters:**
- `format` (string): 'json' or 'csv'

**Returns:** String containing exported data

##### `exportSettings()`
Exports current application settings.

```javascript
const settings = app.exportSettings();
// Returns:
// {
//   theme: object,
//   timestamp: string,
//   version: string
// }
```

##### `importSettings(settings)`
Imports application settings.

```javascript
const success = app.importSettings(settings);
```

**Parameters:**
- `settings` (object): Settings object from exportSettings()

**Returns:** Boolean indicating success

##### `refresh()`
Refreshes all data and charts.

```javascript
app.refresh();
```

##### `destroy()`
Destroys the application and cleans up resources.

```javascript
app.destroy();
```

---

### ThemeManager

Manages themes, color palettes, and visual customization.

#### Constructor
```javascript
import { themeManager } from './js/modules/theme-manager.js';
// themeManager is a singleton instance
```

#### Properties
- `currentTheme` (string): Current theme ID
- `currentMode` (string): 'dark' or 'light'
- `factsMode` (boolean): Facts mode enabled
- `factsColorMode` (boolean): Facts color mode enabled

#### Methods

##### `switchTheme(themeId)`
Switches to a different theme.

```javascript
const success = themeManager.switchTheme('corporate');
```

**Parameters:**
- `themeId` (string): Theme identifier

**Returns:** Boolean indicating success

##### `toggleMode()`
Toggles between light and dark mode.

```javascript
const newMode = themeManager.toggleMode();
// Returns: 'light' or 'dark'
```

##### `toggleFactsMode()`
Toggles facts mode (nutrition label style).

```javascript
const factsMode = themeManager.toggleFactsMode();
// Returns: boolean
```

##### `toggleFactsColorMode()`
Toggles facts color mode.

```javascript
const factsColorMode = themeManager.toggleFactsColorMode();
// Returns: boolean
```

##### `createCustomTheme(name, colors)`
Creates a custom theme.

```javascript
const customTheme = themeManager.createCustomTheme('My Theme', {
    accentStart: '#ff6b35',
    accentEnd: '#f7931e',
    accentAlt: '#ffb347',
    bgStart: '#2c3e50',
    bgEnd: '#34495e',
    cardBg: 'rgba(52, 73, 94, 0.85)',
    positive: '#27ae60',
    negative: '#e74c3c'
});
```

**Parameters:**
- `name` (string): Theme name
- `colors` (object): Color configuration

**Returns:** Theme object

##### `deleteCustomTheme(themeId)`
Deletes a custom theme.

```javascript
const success = themeManager.deleteCustomTheme('custom-123456');
```

##### `getAllThemes()`
Gets all available themes.

```javascript
const themes = themeManager.getAllThemes();
// Returns: Array of theme objects
```

##### `getThemesByCategory(category)`
Gets themes by category.

```javascript
const professionalThemes = themeManager.getThemesByCategory('professional');
```

**Parameters:**
- `category` (string): Theme category

**Returns:** Array of theme objects

##### `resetToDefault()`
Resets to default theme.

```javascript
themeManager.resetToDefault();
```

##### `getCurrentState()`
Gets current theme state.

```javascript
const state = themeManager.getCurrentState();
// Returns:
// {
//   theme: string,
//   mode: string,
//   factsMode: boolean,
//   factsColorMode: boolean,
//   palette: object
// }
```

---

### DataManager

Handles data processing, filtering, and calculations.

#### Constructor
```javascript
import { dataManager } from './js/modules/data-manager.js';
// dataManager is a singleton instance
```

#### Methods

##### `getActiveData()`
Gets currently filtered data.

```javascript
const data = dataManager.getActiveData();
// Returns: Array of data objects
```

##### `getAllData()`
Gets all processed data.

```javascript
const allData = dataManager.getAllData();
// Returns: Array of data objects
```

##### `setDateFilter(from, to, preset)`
Sets date range filter.

```javascript
dataManager.setDateFilter(
    new Date('2024-01-01'),
    new Date('2024-12-31'),
    'custom'
);
```

**Parameters:**
- `from` (Date|null): Start date
- `to` (Date|null): End date
- `preset` (string|null): Preset identifier

##### `applyPresetFilter(preset)`
Applies a preset filter.

```javascript
dataManager.applyPresetFilter('ytd');
```

**Parameters:**
- `preset` (string): 'full', 'ytd', 'last3', 'last6', 'last12'

##### `clearFilter()`
Clears current filter.

```javascript
dataManager.clearFilter();
```

##### `searchData(searchTerm)`
Searches data.

```javascript
const results = dataManager.searchData('2024');
```

**Parameters:**
- `searchTerm` (string): Search term

**Returns:** Array of matching data objects

##### `calculateStats()`
Calculates summary statistics.

```javascript
const stats = dataManager.calculateStats();
// Returns:
// {
//   totalWaste: number,
//   avgMargin: number,
//   avgWastePercent: number,
//   monthCount: number
// }
```

##### `calculateYearlyAverages()`
Calculates yearly averages.

```javascript
const yearlyData = dataManager.calculateYearlyAverages();
// Returns: Object with year keys and average data
```

##### `calculateMovingAverage(data, windowSize)`
Calculates moving average.

```javascript
const ma = dataManager.calculateMovingAverage([1, 2, 3, 4, 5], 3);
// Returns: Array with moving average values
```

##### `exportData(format)`
Exports data.

```javascript
const jsonData = dataManager.exportData('json');
const csvData = dataManager.exportData('csv');
```

##### `formatHUF(amount)`
Formats currency value.

```javascript
const formatted = dataManager.formatHUF(123456);
// Returns: "123 456 Ft"
```

##### `addEventListener(event, callback)`
Adds event listener.

```javascript
dataManager.addEventListener('dataChanged', (data) => {
    console.log('Data changed:', data);
});
```

**Events:**
- `dataChanged`: Fired when active data changes

---

### ChartManager

Manages Chart.js instances and chart-related functionality.

#### Constructor
```javascript
import { ChartManager } from './js/modules/chart-manager.js';

const chartManager = new ChartManager(dataManager, themeManager);
```

#### Methods

##### `createTrendChart(canvasId, data)`
Creates a trend chart.

```javascript
const chart = chartManager.createTrendChart('trendChart', data);
```

**Parameters:**
- `canvasId` (string): Canvas element ID
- `data` (Array): Chart data

**Returns:** Chart.js instance

##### `createYearlyChart(canvasId, yearlyData)`
Creates a yearly comparison chart.

```javascript
const chart = chartManager.createYearlyChart('yearlyChart', yearlyData);
```

##### `createMiniChart(canvasId, data, type)`
Creates a mini chart.

```javascript
const chart = chartManager.createMiniChart('miniChart', data, 'line');
```

**Parameters:**
- `canvasId` (string): Canvas element ID
- `data` (Array): Chart data
- `type` (string): 'line' or 'bar'

##### `setTrendMetric(metric)`
Sets the trend chart metric.

```javascript
chartManager.setTrendMetric('waste'); // 'waste', 'margin', 'wastePct'
```

##### `setMovingAverageWindow(window)`
Sets moving average window.

```javascript
chartManager.setMovingAverageWindow(6);
```

##### `addPinnedTooltip(chart, point)`
Adds a pinned tooltip.

```javascript
chartManager.addPinnedTooltip(chart, point);
```

##### `clearPinnedTooltips()`
Clears all pinned tooltips.

```javascript
chartManager.clearPinnedTooltips();
```

##### `updateAllCharts(data)`
Updates all charts with new data.

```javascript
chartManager.updateAllCharts(data);
```

##### `destroyAllCharts()`
Destroys all chart instances.

```javascript
chartManager.destroyAllCharts();
```

##### `getChart(chartId)`
Gets a chart instance by ID.

```javascript
const chart = chartManager.getChart('trendChart');
```

---

### UIComponents

Manages UI components and interface interactions.

#### Constructor
```javascript
import { UIComponents } from './js/modules/ui-components.js';

const uiComponents = new UIComponents(dataManager, themeManager);
```

#### Methods

##### `updateStatsDisplay(data)`
Updates statistics display.

```javascript
uiComponents.updateStatsDisplay(data);
```

##### `updateTableDisplay(data)`
Updates data table display.

```javascript
uiComponents.updateTableDisplay(data);
```

##### `updateFilterBadge(filter)`
Updates filter badge display.

```javascript
uiComponents.updateFilterBadge(filter);
```

---

## 🎯 Events

### Custom Events

#### `dashboardReady`
Fired when the dashboard is fully initialized.

```javascript
document.addEventListener('dashboardReady', (event) => {
    const app = event.detail.app;
    console.log('Dashboard ready!');
});
```

#### `themechange`
Fired when theme changes.

```javascript
document.addEventListener('themechange', (event) => {
    const { theme, mode, factsMode, palette } = event.detail;
    console.log('Theme changed:', theme);
});
```

#### `trendMetricChange`
Fired when trend metric changes.

```javascript
document.addEventListener('trendMetricChange', (event) => {
    const { metric } = event.detail;
    console.log('Metric changed:', metric);
});
```

#### `movingAverageChange`
Fired when moving average window changes.

```javascript
document.addEventListener('movingAverageChange', (event) => {
    const { window } = event.detail;
    console.log('MA window changed:', window);
});
```

#### `clearPinnedTooltips`
Fired when pinned tooltips should be cleared.

```javascript
document.addEventListener('clearPinnedTooltips', () => {
    console.log('Clear pinned tooltips');
});
```

---

## 🔧 Configuration Objects

### Theme Object
```javascript
{
    id: 'theme-id',
    name: 'Theme Name',
    category: 'professional',
    description: 'Theme description',
    accessibility: 'AAA',
    vars: {
        '--accent-start': '#6366f1',
        '--accent-end': '#8b5cf6',
        // ... other CSS variables
    }
}
```

### Data Object
```javascript
{
    date: Date,
    dateString: 'January 2024',
    wasteHUF: 123456,
    marginPercent: 25.5,
    wastePercent: 2.8,
    year: 2024,
    month: 1,
    quarter: 1
}
```

### Filter Object
```javascript
{
    from: Date | null,
    to: Date | null,
    preset: 'ytd' | null
}
```

---

## 🚨 Error Handling

### Global Error Events
```javascript
// Uncaught errors
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
});

// Unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
});
```

### Module-Specific Errors
Each module handles its own errors gracefully and provides fallback behavior when possible.
