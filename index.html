<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pénzügyi Dashboard v2.0</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    
    <!-- CSS Modules -->
    <link rel="stylesheet" href="styles/variables.css">
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/themes.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <link rel="stylesheet" href="styles/utilities.css">
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="panel-toggle" id="openCustomizer" title="Testreszabás / Color Customizer">
        🎨 Színek
    </button>

    <!-- Data Panel Toggle -->
    <button class="panel-toggle" id="dataPanelToggle" title="Adatok és Szűrők" style="right: 120px;">
        📊 Adatok
    </button>

    <!-- Customizer Panel -->
    <div class="panel" id="customizerPanel" aria-label="Szín testreszabás panel">
        <button class="panel-close" aria-label="Bezárás">×</button>
        <h3>🎨 Színek és Témák</h3>
        
        <!-- Theme Controls -->
        <div class="form-group">
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="toggleTheme">Váltás Világos Témára</button>
                <button type="button" class="btn btn-secondary" id="toggleFacts">Táblázat Stílus: Modern ➜ Label</button>
                <button type="button" class="btn btn-secondary" id="toggleFactsColor">Label Színezés: KI</button>
            </div>
        </div>

        <!-- Color Controls -->
        <div class="form-group">
            <label class="form-label">Egyedi Színek</label>
            <div class="form-row">
                <label for="accentStart">Fő Szín 1</label>
                <input type="color" id="accentStart" value="#6366f1">
            </div>
            <div class="form-row">
                <label for="accentEnd">Fő Szín 2</label>
                <input type="color" id="accentEnd" value="#8b5cf6">
            </div>
            <div class="form-row">
                <label for="accentAlt">Alternatív</label>
                <input type="color" id="accentAlt" value="#a855f7">
            </div>
            <div class="form-row">
                <label for="bgStart">Háttér 1</label>
                <input type="color" id="bgStart" value="#0f172a">
            </div>
            <div class="form-row">
                <label for="bgEnd">Háttér 2</label>
                <input type="color" id="bgEnd" value="#1e293b">
            </div>
            <div class="form-row">
                <label for="cardBg">Kártya Háttér</label>
                <input type="color" id="cardBg" value="#1e293b">
            </div>
        </div>

        <!-- Palette Selector -->
        <div class="form-group">
            <label class="form-label">Előre Definiált Témák</label>
            <div id="paletteContainer">
                <!-- Palettes will be populated by JavaScript -->
            </div>
        </div>

        <!-- Reset Button -->
        <div class="form-actions">
            <button type="button" class="btn btn-ghost" id="resetColors">Alapértelmezett Visszaállítása</button>
        </div>
    </div>

    <!-- Data Panel -->
    <div class="panel" id="dataPanel" aria-label="Adat szűrés panel">
        <button class="panel-close" aria-label="Bezárás">×</button>
        <h3>📊 Adatok és Szűrők</h3>
        
        <!-- Date Filter -->
        <div class="form-group">
            <label class="form-label">Dátum Szűrő</label>
            <div class="form-row">
                <label for="dateFrom">Kezdő Dátum</label>
                <input type="date" id="dateFrom">
            </div>
            <div class="form-row">
                <label for="dateTo">Vég Dátum</label>
                <input type="date" id="dateTo">
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-primary" id="applyDateFilter">Alkalmazás</button>
                <button type="button" class="btn btn-secondary" id="resetDateFilter">Törlés</button>
            </div>
        </div>

        <!-- Preset Filters -->
        <div class="form-group">
            <label class="form-label">Gyors Szűrők</label>
            <div class="toggle-group">
                <button type="button" class="preset-btn" data-preset="ytd">YTD</button>
                <button type="button" class="preset-btn" data-preset="last3">3 hó</button>
                <button type="button" class="preset-btn" data-preset="last6">6 hó</button>
                <button type="button" class="preset-btn" data-preset="last12">12 hó</button>
                <button type="button" class="preset-btn" data-preset="full">Teljes</button>
            </div>
        </div>

        <!-- Search -->
        <div class="form-group">
            <label class="form-label" for="searchInput">Keresés</label>
            <input type="text" id="searchInput" placeholder="Keresés az adatokban...">
        </div>

        <!-- Export -->
        <div class="form-group">
            <label class="form-label">Export</label>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="exportJSON">JSON</button>
                <button type="button" class="btn btn-secondary" id="exportCSV">CSV</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Pénzügyi Dashboard</h1>
            <p>Selejt és árrés elemzés v2.0</p>
            <span class="badge badge-info" id="dateFilterBadge" style="display: none;">Szűrő aktív</span>
        </header>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalWaste">0 Ft</div>
                <div class="stat-label">Összes Selejt</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgMargin">0%</div>
                <div class="stat-label">Átlag Árrés</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgWastePercent">0%</div>
                <div class="stat-label">Átlag Selejt %</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="monthCount">0</div>
                <div class="stat-label">Hónapok Száma</div>
            </div>
        </div>

        <!-- Main Charts -->
        <div class="dashboard-grid">
            <!-- Trend Chart -->
            <div class="card">
                <div class="chart-header-row">
                    <h2><span class="icon">📈</span> Trend Elemzés</h2>
                    <div class="chart-controls">
                        <div class="toggle-group" id="trendControls">
                            <button type="button" class="toggle-btn active mt-btn" data-metric="waste">Selejt HUF</button>
                            <button type="button" class="toggle-btn mt-btn" data-metric="margin">Árrés %</button>
                            <button type="button" class="toggle-btn mt-btn" data-metric="wastePct">Selejt %</button>
                        </div>
                        <select id="maSelect" title="Mozgó átlag">
                            <option value="0">Mozgó átlag</option>
                            <option value="3">3 hónap</option>
                            <option value="6">6 hónap</option>
                            <option value="12">12 hónap</option>
                        </select>
                        <button type="button" class="btn btn-small" id="clearPins">Jelölők törlése</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>

            <!-- Yearly Comparison -->
            <div class="card yearly-card">
                <h2><span class="icon">📊</span> Éves Összehasonlítás</h2>
                <div class="chart-container">
                    <canvas id="yearlyChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Mini Charts Row -->
        <div class="two-col-row">
            <div class="mini-card">
                <h4><span class="icon">💰</span> Selejt HUF</h4>
                <span class="value-badge" id="miniWasteValue">0 Ft</span>
                <canvas id="miniWasteChart"></canvas>
            </div>
            <div class="mini-card">
                <h4><span class="icon">📈</span> Árrés %</h4>
                <span class="value-badge" id="miniMarginValue">0%</span>
                <canvas id="miniMarginChart"></canvas>
            </div>
            <div class="mini-card">
                <h4><span class="icon">⚠️</span> Selejt %</h4>
                <span class="value-badge" id="miniWastePctValue">0%</span>
                <canvas id="miniWastePctChart"></canvas>
            </div>
        </div>

        <!-- Data Table -->
        <div class="card table-container">
            <h2><span class="icon">📋</span> Részletes Adatok</h2>
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Dátum</th>
                            <th>Selejt HUF</th>
                            <th>Árrés %</th>
                            <th>Selejt %</th>
                            <th>Változás</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <!-- Data will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script type="module" src="js/app.js"></script>
</body>
</html>
