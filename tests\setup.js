/**
 * Test setup file for Vitest
 * Configures global test environment and mocks
 */

import { vi } from 'vitest';

// Mock Chart.js
global.Chart = {
  register: vi.fn(),
  defaults: {
    onError: vi.fn()
  }
};

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
};
global.localStorage = localStorageMock;

// Mock sessionStorage
global.sessionStorage = localStorageMock;

// Mock window.matchMedia
global.matchMedia = vi.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn()
}));

// Mock performance API
global.performance = {
  ...global.performance,
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000
  },
  timing: {
    navigationStart: 0,
    loadEventEnd: 1000,
    domContentLoadedEventEnd: 500
  }
};

// Mock Intl.NumberFormat
global.Intl = {
  NumberFormat: vi.fn().mockImplementation(() => ({
    format: vi.fn().mockImplementation(num => `${num} Ft`)
  }))
};

// Mock fetch
global.fetch = vi.fn();

// Mock CSS custom properties
const mockGetComputedStyle = vi.fn().mockReturnValue({
  getPropertyValue: vi.fn().mockReturnValue('#6366f1')
});
global.getComputedStyle = mockGetComputedStyle;

// Mock DOM methods
global.document.documentElement.style.setProperty = vi.fn();

// Setup DOM environment
beforeEach(() => {
  // Clear all mocks before each test
  vi.clearAllMocks();
  
  // Reset localStorage
  localStorageMock.getItem.mockReturnValue(null);
  
  // Reset DOM
  document.body.innerHTML = '';
  document.head.innerHTML = '';
});

// Global test utilities
global.createMockElement = (id, tagName = 'div') => {
  const element = document.createElement(tagName);
  element.id = id;
  document.body.appendChild(element);
  return element;
};

global.createMockCanvas = (id) => {
  const canvas = document.createElement('canvas');
  canvas.id = id;
  canvas.getContext = vi.fn().mockReturnValue({
    createLinearGradient: vi.fn().mockReturnValue({
      addColorStop: vi.fn()
    }),
    fillText: vi.fn(),
    save: vi.fn(),
    restore: vi.fn()
  });
  document.body.appendChild(canvas);
  return canvas;
};

global.mockChartInstance = {
  data: {
    labels: [],
    datasets: []
  },
  options: {},
  update: vi.fn(),
  destroy: vi.fn(),
  getElementsAtEventForMode: vi.fn().mockReturnValue([]),
  getDatasetMeta: vi.fn().mockReturnValue({
    data: []
  }),
  canvas: {
    parentNode: {
      querySelector: vi.fn(),
      style: {},
      appendChild: vi.fn()
    }
  },
  ctx: {
    createLinearGradient: vi.fn().mockReturnValue({
      addColorStop: vi.fn()
    })
  }
};

// Mock Chart constructor
global.Chart = vi.fn().mockImplementation(() => global.mockChartInstance);
global.Chart.register = vi.fn();
global.Chart.defaults = { onError: vi.fn() };

console.log('✅ Test environment setup complete');
