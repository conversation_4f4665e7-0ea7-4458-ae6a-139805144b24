/**
 * ===================================================================
 * ADVANCED COLOR PALETTE SYSTEM
 * Comprehensive color themes with accessibility and customization
 * ===================================================================
 */

/**
 * Color palette configuration object
 * Each palette contains semantic color mappings and metadata
 */
export const COLOR_PALETTES = {
    // ===============================
    // PROFESSIONAL THEMES
    // ===============================
    
    corporate: {
        id: 'corporate',
        name: 'Corporate Blue',
        category: 'professional',
        description: 'Professional corporate theme with blue accents',
        accessibility: 'AAA',
        vars: {
            '--accent-start': '#1e40af',
            '--accent-end': '#3b82f6',
            '--accent-alt': '#6366f1',
            '--accent-neutral': '#64748b',
            '--accent-warn': '#f59e0b',
            '--bg-gradient-start': '#0f172a',
            '--bg-gradient-end': '#1e293b',
            '--card-bg': 'rgba(30, 64, 175, 0.15)',
            '--positive': '#10b981',
            '--negative': '#ef4444'
        }
    },

    executive: {
        id: 'executive',
        name: 'Executive Gray',
        category: 'professional',
        description: 'Sophisticated grayscale theme for executive dashboards',
        accessibility: 'AAA',
        vars: {
            '--accent-start': '#374151',
            '--accent-end': '#6b7280',
            '--accent-alt': '#9ca3af',
            '--accent-neutral': '#d1d5db',
            '--accent-warn': '#f59e0b',
            '--bg-gradient-start': '#111827',
            '--bg-gradient-end': '#1f2937',
            '--card-bg': 'rgba(55, 65, 81, 0.20)',
            '--positive': '#059669',
            '--negative': '#dc2626'
        }
    },

    financial: {
        id: 'financial',
        name: 'Financial Green',
        category: 'professional',
        description: 'Finance-focused theme with green and gold accents',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#065f46',
            '--accent-end': '#059669',
            '--accent-alt': '#10b981',
            '--accent-neutral': '#6b7280',
            '--accent-warn': '#d97706',
            '--bg-gradient-start': '#064e3b',
            '--bg-gradient-end': '#065f46',
            '--card-bg': 'rgba(6, 95, 70, 0.25)',
            '--positive': '#22c55e',
            '--negative': '#dc2626'
        }
    },

    // ===============================
    // MODERN THEMES
    // ===============================

    aurora: {
        id: 'aurora',
        name: 'Aurora Borealis',
        category: 'modern',
        description: 'Inspired by northern lights with blue and yellow',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#0D3B66',
            '--accent-end': '#F4D35E',
            '--accent-alt': '#EE964B',
            '--accent-neutral': '#FAF0CA',
            '--accent-warn': '#F95738',
            '--bg-gradient-start': '#0D3B66',
            '--bg-gradient-end': '#1a5490',
            '--card-bg': 'rgba(13, 59, 102, 0.30)',
            '--positive': '#22c55e',
            '--negative': '#F95738'
        }
    },

    cyberpunk: {
        id: 'cyberpunk',
        name: 'Cyberpunk Neon',
        category: 'modern',
        description: 'Futuristic neon theme with high contrast',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#0a0221',
            '--accent-end': '#63adf2',
            '--accent-alt': '#fe5f55',
            '--accent-neutral': '#ffc145',
            '--accent-warn': '#ff6b35',
            '--bg-gradient-start': '#0a0221',
            '--bg-gradient-end': '#1a0b3d',
            '--card-bg': 'rgba(10, 2, 33, 0.40)',
            '--positive': '#00ff88',
            '--negative': '#ff0055'
        }
    },

    synthwave: {
        id: 'synthwave',
        name: 'Synthwave Retro',
        category: 'modern',
        description: '80s-inspired retro futuristic theme',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#ff006e',
            '--accent-end': '#8338ec',
            '--accent-alt': '#3a86ff',
            '--accent-neutral': '#ffbe0b',
            '--accent-warn': '#fb5607',
            '--bg-gradient-start': '#240046',
            '--bg-gradient-end': '#3c096c',
            '--card-bg': 'rgba(131, 56, 236, 0.20)',
            '--positive': '#06ffa5',
            '--negative': '#ff006e'
        }
    },

    // ===============================
    // NATURE THEMES
    // ===============================

    forest: {
        id: 'forest',
        name: 'Deep Forest',
        category: 'nature',
        description: 'Calming forest greens with earth tones',
        accessibility: 'AAA',
        vars: {
            '--accent-start': '#2d6a4f',
            '--accent-end': '#40916c',
            '--accent-alt': '#95d5b2',
            '--accent-neutral': '#d8f3dc',
            '--accent-warn': '#ffb703',
            '--bg-gradient-start': '#1b4332',
            '--bg-gradient-end': '#2d6a4f',
            '--card-bg': 'rgba(45, 106, 79, 0.25)',
            '--positive': '#52b788',
            '--negative': '#d62828'
        }
    },

    ocean: {
        id: 'ocean',
        name: 'Ocean Depths',
        category: 'nature',
        description: 'Deep ocean blues with aqua highlights',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#003566',
            '--accent-end': '#0077b6',
            '--accent-alt': '#00b4d8',
            '--accent-neutral': '#90e0ef',
            '--accent-warn': '#ffd60a',
            '--bg-gradient-start': '#001d3d',
            '--bg-gradient-end': '#003566',
            '--card-bg': 'rgba(0, 53, 102, 0.30)',
            '--positive': '#06ffa5',
            '--negative': '#ff4081'
        }
    },

    sunset: {
        id: 'sunset',
        name: 'Sunset Calm',
        category: 'nature',
        description: 'Warm sunset colors with purple undertones',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#22223b',
            '--accent-end': '#4a4e69',
            '--accent-alt': '#9a8c98',
            '--accent-neutral': '#c9ada7',
            '--accent-warn': '#f28482',
            '--bg-gradient-start': '#22223b',
            '--bg-gradient-end': '#4a4e69',
            '--card-bg': 'rgba(34, 34, 59, 0.35)',
            '--positive': '#a8dadc',
            '--negative': '#e63946'
        }
    },

    // ===============================
    // HIGH CONTRAST THEMES
    // ===============================

    monochrome: {
        id: 'monochrome',
        name: 'Monochrome',
        category: 'accessibility',
        description: 'High contrast black and white theme',
        accessibility: 'AAA',
        vars: {
            '--accent-start': '#000000',
            '--accent-end': '#333333',
            '--accent-alt': '#666666',
            '--accent-neutral': '#999999',
            '--accent-warn': '#000000',
            '--bg-gradient-start': '#ffffff',
            '--bg-gradient-end': '#f5f5f5',
            '--card-bg': 'rgba(0, 0, 0, 0.05)',
            '--positive': '#000000',
            '--negative': '#000000'
        }
    },

    highContrast: {
        id: 'highContrast',
        name: 'High Contrast',
        category: 'accessibility',
        description: 'Maximum contrast for accessibility',
        accessibility: 'AAA',
        vars: {
            '--accent-start': '#0000ff',
            '--accent-end': '#0066cc',
            '--accent-alt': '#6600cc',
            '--accent-neutral': '#333333',
            '--accent-warn': '#ff6600',
            '--bg-gradient-start': '#000000',
            '--bg-gradient-end': '#1a1a1a',
            '--card-bg': 'rgba(255, 255, 255, 0.10)',
            '--positive': '#00ff00',
            '--negative': '#ff0000'
        }
    },

    // ===============================
    // SEASONAL THEMES
    // ===============================

    spring: {
        id: 'spring',
        name: 'Spring Bloom',
        category: 'seasonal',
        description: 'Fresh spring colors with green and pink',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#2d5016',
            '--accent-end': '#56ab2f',
            '--accent-alt': '#a8e6cf',
            '--accent-neutral': '#dcedc1',
            '--accent-warn': '#ff8b94',
            '--bg-gradient-start': '#2d5016',
            '--bg-gradient-end': '#56ab2f',
            '--card-bg': 'rgba(45, 80, 22, 0.25)',
            '--positive': '#7cb342',
            '--negative': '#e57373'
        }
    },

    autumn: {
        id: 'autumn',
        name: 'Autumn Leaves',
        category: 'seasonal',
        description: 'Warm autumn colors with orange and brown',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#8b4513',
            '--accent-end': '#d2691e',
            '--accent-alt': '#daa520',
            '--accent-neutral': '#f4a460',
            '--accent-warn': '#ff6347',
            '--bg-gradient-start': '#654321',
            '--bg-gradient-end': '#8b4513',
            '--card-bg': 'rgba(139, 69, 19, 0.25)',
            '--positive': '#9acd32',
            '--negative': '#dc143c'
        }
    },

    winter: {
        id: 'winter',
        name: 'Winter Frost',
        category: 'seasonal',
        description: 'Cool winter theme with ice blues',
        accessibility: 'AA',
        vars: {
            '--accent-start': '#1e3a8a',
            '--accent-end': '#3b82f6',
            '--accent-alt': '#93c5fd',
            '--accent-neutral': '#dbeafe',
            '--accent-warn': '#f59e0b',
            '--bg-gradient-start': '#0f172a',
            '--bg-gradient-end': '#1e3a8a',
            '--card-bg': 'rgba(30, 58, 138, 0.20)',
            '--positive': '#10b981',
            '--negative': '#ef4444'
        }
    }
};

/**
 * Theme categories for organization
 */
export const THEME_CATEGORIES = {
    professional: {
        name: 'Professional',
        description: 'Business and corporate themes',
        icon: '💼'
    },
    modern: {
        name: 'Modern',
        description: 'Contemporary and futuristic themes',
        icon: '🚀'
    },
    nature: {
        name: 'Nature',
        description: 'Earth-inspired natural themes',
        icon: '🌿'
    },
    accessibility: {
        name: 'Accessibility',
        description: 'High contrast and accessible themes',
        icon: '♿'
    },
    seasonal: {
        name: 'Seasonal',
        description: 'Season-inspired color schemes',
        icon: '🍂'
    }
};

/**
 * Accessibility levels
 */
export const ACCESSIBILITY_LEVELS = {
    AAA: {
        name: 'AAA',
        description: 'Highest accessibility standard',
        contrastRatio: 7.0
    },
    AA: {
        name: 'AA',
        description: 'Standard accessibility compliance',
        contrastRatio: 4.5
    },
    A: {
        name: 'A',
        description: 'Basic accessibility',
        contrastRatio: 3.0
    }
};

/**
 * Get palettes by category
 */
export function getPalettesByCategory(category) {
    return Object.values(COLOR_PALETTES).filter(palette => palette.category === category);
}

/**
 * Get palette by ID
 */
export function getPaletteById(id) {
    return COLOR_PALETTES[id] || null;
}

/**
 * Get all palette IDs
 */
export function getAllPaletteIds() {
    return Object.keys(COLOR_PALETTES);
}

/**
 * Generate CSS custom properties from palette
 */
export function generateCSSVariables(palette) {
    if (!palette || !palette.vars) return '';
    
    return Object.entries(palette.vars)
        .map(([property, value]) => `${property}: ${value};`)
        .join('\n    ');
}

/**
 * Create a custom palette from user input
 */
export function createCustomPalette(name, colors) {
    return {
        id: `custom-${Date.now()}`,
        name: name || 'Custom Theme',
        category: 'custom',
        description: 'User-created custom theme',
        accessibility: 'Unknown',
        vars: {
            '--accent-start': colors.accentStart || '#6366f1',
            '--accent-end': colors.accentEnd || '#8b5cf6',
            '--accent-alt': colors.accentAlt || '#a855f7',
            '--accent-neutral': colors.accentNeutral || '#94a3b8',
            '--accent-warn': colors.accentWarn || '#f59e0b',
            '--bg-gradient-start': colors.bgStart || '#0f172a',
            '--bg-gradient-end': colors.bgEnd || '#1e293b',
            '--card-bg': colors.cardBg || 'rgba(30, 41, 59, 0.85)',
            '--positive': colors.positive || '#10b981',
            '--negative': colors.negative || '#ef4444'
        }
    };
}
