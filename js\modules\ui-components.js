/**
 * ===================================================================
 * UI COMPONENTS MODULE
 * Reusable UI components and interface management
 * ===================================================================
 */

/**
 * UI Components Manager Class
 * Handles UI component creation and management
 */
export class UIComponents {
    constructor(dataManager, themeManager) {
        this.dataManager = dataManager;
        this.themeManager = themeManager;
        this.panels = new Map();
        this.components = new Map();
        
        this.init();
    }

    /**
     * Initialize UI components
     */
    init() {
        this.setupEventListeners();
        this.initializePanels();
        this.initializeComponents();
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // Listen for data changes
        this.dataManager.addEventListener('dataChanged', (data) => {
            this.updateStatsDisplay(data.activeData);
            this.updateTableDisplay(data.activeData);
            this.updateFilterBadge(data.filter);
        });

        // Listen for theme changes
        document.addEventListener('themechange', (event) => {
            this.updateThemeControls(event.detail);
        });

        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Close panels on outside click
        document.addEventListener('click', (e) => {
            this.handleOutsideClick(e);
        });
    }

    /**
     * Initialize panels
     */
    initializePanels() {
        this.initializeCustomizerPanel();
        this.initializeDataPanel();
    }

    /**
     * Initialize customizer panel
     */
    initializeCustomizerPanel() {
        const panel = new Panel('customizerPanel', 'openCustomizer');
        
        // Add color controls
        this.createColorControls(panel);
        
        // Add palette selector
        this.createPaletteSelector(panel);
        
        // Add theme controls
        this.createThemeControls(panel);
        
        this.panels.set('customizer', panel);
    }

    /**
     * Initialize data panel
     */
    initializeDataPanel() {
        const panel = new Panel('dataPanel', 'dataPanelToggle');
        
        // Add date filter controls
        this.createDateFilterControls(panel);
        
        // Add search functionality
        this.createSearchControls(panel);
        
        this.panels.set('data', panel);
    }

    /**
     * Initialize other components
     */
    initializeComponents() {
        this.initializeStatsCards();
        this.initializeTrendControls();
        this.initializeTableControls();
    }

    /**
     * Create color controls
     */
    createColorControls(panel) {
        const colorMappings = {
            accentStart: '--accent-start',
            accentEnd: '--accent-end',
            accentAlt: '--accent-alt',
            bgStart: '--bg-gradient-start',
            bgEnd: '--bg-gradient-end',
            cardBg: '--card-bg'
        };

        Object.entries(colorMappings).forEach(([id, varName]) => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('input', (e) => {
                    const value = varName === '--card-bg' ? 
                        this.hexToRgba(e.target.value, 0.85) : 
                        e.target.value;
                    
                    this.updateCSSVariable(varName, value);
                    this.clearPaletteSelection();
                });
            }
        });
    }

    /**
     * Create palette selector
     */
    createPaletteSelector(panel) {
        const container = document.getElementById('paletteContainer');
        if (!container) return;

        const palettes = this.themeManager.getAllThemes();
        const palettesByCategory = this.groupByCategory(palettes);

        let html = '<div class="palette-grid" id="paletteGrid">';
        
        Object.entries(palettesByCategory).forEach(([category, themes]) => {
            html += `<div class="palette-category">
                <h5 class="palette-category-title">${category}</h5>
                <div class="palette-category-grid">`;
            
            themes.forEach(theme => {
                const swatches = this.createPaletteSwatches(theme);
                html += `
                    <button type="button" 
                            class="palette-btn" 
                            data-palette="${theme.id}" 
                            title="${theme.description}"
                            aria-label="${theme.name}">
                        ${swatches}
                        <span class="palette-name">${theme.name}</span>
                    </button>`;
            });
            
            html += '</div></div>';
        });
        
        html += '</div>';
        container.innerHTML = html;

        // Add event listeners
        container.addEventListener('click', (e) => {
            const btn = e.target.closest('.palette-btn');
            if (btn) {
                this.applyPalette(btn.dataset.palette);
            }
        });
    }

    /**
     * Create theme controls
     */
    createThemeControls(panel) {
        // Theme toggle
        const themeToggle = document.getElementById('toggleTheme');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                const newMode = this.themeManager.toggleMode();
                themeToggle.textContent = newMode === 'light' ? 
                    'Váltás Sötét Témára' : 
                    'Váltás Világos Témára';
            });
        }

        // Facts mode toggle
        const factsToggle = document.getElementById('toggleFacts');
        if (factsToggle) {
            factsToggle.addEventListener('click', () => {
                const factsMode = this.themeManager.toggleFactsMode();
                factsToggle.textContent = factsMode ? 
                    'Táblázat Stílus: Label ➜ Modern' : 
                    'Táblázat Stílus: Modern ➜ Label';
            });
        }

        // Facts color toggle
        const factsColorToggle = document.getElementById('toggleFactsColor');
        if (factsColorToggle) {
            factsColorToggle.addEventListener('click', () => {
                const factsColorMode = this.themeManager.toggleFactsColorMode();
                factsColorToggle.textContent = `Label Színezés: ${factsColorMode ? 'BE' : 'KI'}`;
            });
        }

        // Reset button
        const resetBtn = document.getElementById('resetColors');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.themeManager.resetToDefault();
                this.populateColorInputs();
            });
        }
    }

    /**
     * Create date filter controls
     */
    createDateFilterControls(panel) {
        const fromInput = document.getElementById('dateFrom');
        const toInput = document.getElementById('dateTo');
        const applyBtn = document.getElementById('applyDateFilter');
        const resetBtn = document.getElementById('resetDateFilter');

        // Apply filter
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                const from = fromInput?.value ? new Date(fromInput.value) : null;
                const to = toInput?.value ? new Date(toInput.value) : null;
                this.dataManager.setDateFilter(from, to);
            });
        }

        // Reset filter
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.dataManager.clearFilter();
                if (fromInput) fromInput.value = '';
                if (toInput) toInput.value = '';
                this.clearPresetSelection();
            });
        }

        // Preset buttons
        const presetButtons = document.querySelectorAll('.preset-btn[data-preset]');
        presetButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.dataManager.applyPresetFilter(btn.dataset.preset);
                this.highlightPreset(btn.dataset.preset);
                this.updateDateInputs();
            });
        });
    }

    /**
     * Create search controls
     */
    createSearchControls(panel) {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const filteredData = this.dataManager.searchData(e.target.value);
                    this.updateTableDisplay(filteredData);
                }, 300);
            });
        }
    }

    /**
     * Initialize stats cards
     */
    initializeStatsCards() {
        this.statsElements = {
            totalWaste: document.getElementById('totalWaste'),
            avgMargin: document.getElementById('avgMargin'),
            avgWastePercent: document.getElementById('avgWastePercent'),
            monthCount: document.getElementById('monthCount')
        };
    }

    /**
     * Initialize trend controls
     */
    initializeTrendControls() {
        const controls = document.getElementById('trendControls');
        if (!controls) return;

        // Metric toggle buttons
        controls.addEventListener('click', (e) => {
            const btn = e.target.closest('.mt-btn');
            if (btn) {
                controls.querySelectorAll('.mt-btn').forEach(b => 
                    b.classList.toggle('active', b === btn)
                );
                
                // Dispatch event for chart manager
                document.dispatchEvent(new CustomEvent('trendMetricChange', {
                    detail: { metric: btn.dataset.metric }
                }));
            }
        });

        // Moving average selector
        const maSelect = document.getElementById('maSelect');
        if (maSelect) {
            maSelect.addEventListener('change', () => {
                document.dispatchEvent(new CustomEvent('movingAverageChange', {
                    detail: { window: parseInt(maSelect.value, 10) }
                }));
            });
        }

        // Clear pins button
        const clearPinsBtn = document.getElementById('clearPins');
        if (clearPinsBtn) {
            clearPinsBtn.addEventListener('click', () => {
                document.dispatchEvent(new CustomEvent('clearPinnedTooltips'));
            });
        }
    }

    /**
     * Initialize table controls
     */
    initializeTableControls() {
        // Table will be updated via data manager events
    }

    /**
     * Update stats display
     */
    updateStatsDisplay(data) {
        const stats = this.dataManager.calculateStats();
        
        if (this.statsElements.totalWaste) {
            this.statsElements.totalWaste.textContent = this.dataManager.formatHUF(stats.totalWaste);
        }
        
        if (this.statsElements.avgMargin) {
            this.statsElements.avgMargin.textContent = stats.avgMargin.toFixed(1) + '%';
        }
        
        if (this.statsElements.avgWastePercent) {
            this.statsElements.avgWastePercent.textContent = stats.avgWastePercent.toFixed(2) + '%';
        }
        
        if (this.statsElements.monthCount) {
            this.statsElements.monthCount.textContent = stats.monthCount.toString();
        }
    }

    /**
     * Update table display
     */
    updateTableDisplay(data) {
        const tbody = document.getElementById('dataTableBody');
        if (!tbody) return;

        tbody.innerHTML = '';
        
        if (!data.length) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">Nincs adat</td></tr>';
            return;
        }

        const maxWaste = Math.max(...data.map(d => d.wasteHUF));
        let previousRow = null;

        data.forEach(row => {
            const tr = this.createTableRow(row, previousRow, maxWaste);
            tbody.appendChild(tr);
            previousRow = row;
        });
    }

    /**
     * Create table row
     */
    createTableRow(row, previousRow, maxWaste) {
        const tr = document.createElement('tr');
        
        // Calculate waste band and deltas
        const wasteBand = this.getWasteBand(row.wastePercent);
        const deltas = this.calculateDeltas(row, previousRow);
        const wasteBarWidth = ((row.wasteHUF / maxWaste) * 100).toFixed(1);

        tr.dataset.wasteBand = wasteBand;
        if (row.marginPercent < 24.0) tr.dataset.alert = 'low-margin';
        if (row.wastePercent > 3.0) tr.dataset.alert = 'high-waste';

        tr.innerHTML = `
            <td>${row.dateString}</td>
            <td class="text-right w-cell">
                <span class="w-amt">${this.dataManager.formatHUF(row.wasteHUF)}</span>
                <span class="bar-wrap">
                    <span class="bar-fill" style="--w:${wasteBarWidth}%"></span>
                </span>
            </td>
            <td class="text-right">
                <span class="percentage">${row.marginPercent.toFixed(1)}%</span>
            </td>
            <td class="text-right">
                <span class="percentage">${row.wastePercent.toFixed(2)}%</span>
            </td>
            <td class="text-right change-col">
                ${this.createDeltaDisplay(deltas)}
            </td>
        `;

        return tr;
    }

    /**
     * Create delta display for table
     */
    createDeltaDisplay(deltas) {
        const marginLine = this.createDeltaLine('ÁRRÉS', deltas.margin);
        const wasteLine = this.createDeltaLine('SELEJT%', deltas.waste);
        return marginLine + wasteLine;
    }

    /**
     * Create individual delta line
     */
    createDeltaLine(label, delta) {
        if (delta === null) {
            return `<span class="delta-line neutral">
                <span class="delta-metric">${label}</span>
                <span class="delta-val">—</span>
            </span>`;
        }

        const arrow = delta.value > 0 ? '▲' : '▼';
        const className = delta.isPositive ? 'pos' : 'neg';
        const sign = delta.value > 0 ? '+' : '';
        
        return `<span class="delta-line ${className}">
            <span class="delta-metric">${label}</span>
            <span class="delta-arrow">${arrow}</span>
            <span class="delta-val">${sign}${Math.abs(delta.value).toFixed(2)}%</span>
        </span>`;
    }

    /**
     * Calculate deltas between current and previous row
     */
    calculateDeltas(current, previous) {
        if (!previous) {
            return { margin: null, waste: null };
        }

        const marginDiff = current.marginPercent - previous.marginPercent;
        const wasteDiff = current.wastePercent - previous.wastePercent;

        return {
            margin: {
                value: marginDiff,
                isPositive: marginDiff > 0
            },
            waste: {
                value: wasteDiff,
                isPositive: wasteDiff < 0 // Lower waste is positive
            }
        };
    }

    /**
     * Get waste band classification
     */
    getWasteBand(wastePercent) {
        if (wastePercent > 3.0) return 'high';
        if (wastePercent < 2.0) return 'low';
        return 'mid';
    }

    /**
     * Update filter badge
     */
    updateFilterBadge(filter) {
        const badge = document.getElementById('dateFilterBadge');
        if (!badge) return;

        if (!filter) {
            badge.style.display = 'none';
            return;
        }

        badge.style.display = 'inline-block';
        
        let label = '';
        if (filter.preset) {
            const presetMap = {
                ytd: 'YTD',
                last3: 'Utolsó 3',
                last6: 'Utolsó 6',
                last12: 'Utolsó 12',
                full: 'Teljes'
            };
            label = presetMap[filter.preset] || filter.preset;
        } else {
            const fmt = (date) => date.toLocaleDateString('hu-HU', {
                year: '2-digit',
                month: '2-digit',
                day: '2-digit'
            });
            label = `${filter.from ? fmt(filter.from) : '…'} - ${filter.to ? fmt(filter.to) : '…'}`;
        }

        const activeData = this.dataManager.getActiveData();
        badge.textContent = `${label} (${activeData.length} hó)`;
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        // Escape key closes panels
        if (e.key === 'Escape') {
            this.panels.forEach(panel => panel.close());
        }

        // Ctrl/Cmd + K opens search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                this.panels.get('data')?.open();
                setTimeout(() => searchInput.focus(), 100);
            }
        }
    }

    /**
     * Handle outside clicks
     */
    handleOutsideClick(e) {
        this.panels.forEach((panel, key) => {
            if (panel.isOpen() && !panel.contains(e.target) && !panel.isToggleButton(e.target)) {
                panel.close();
            }
        });
    }

    /**
     * Utility methods
     */
    hexToRgba(hex, alpha = 0.85) {
        const h = hex.replace('#', '');
        const bigint = parseInt(h, 16);
        const r = (bigint >> 16) & 255;
        const g = (bigint >> 8) & 255;
        const b = bigint & 255;
        return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    updateCSSVariable(property, value) {
        document.documentElement.style.setProperty(property, value);
    }

    groupByCategory(items) {
        return items.reduce((groups, item) => {
            const category = item.category || 'other';
            if (!groups[category]) groups[category] = [];
            groups[category].push(item);
            return groups;
        }, {});
    }

    createPaletteSwatches(theme) {
        const colors = ['--accent-start', '--accent-end', '--accent-alt', '--accent-neutral', '--accent-warn'];
        return colors.map(colorVar => {
            const color = theme.vars[colorVar] || '#000';
            return `<span class="palette-swatch" style="background:${color}"></span>`;
        }).join('');
    }

    applyPalette(paletteId) {
        this.themeManager.switchTheme(paletteId);
        this.highlightPalette(paletteId);
        this.populateColorInputs();
    }

    highlightPalette(paletteId) {
        const buttons = document.querySelectorAll('.palette-btn');
        buttons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.palette === paletteId);
        });
    }

    clearPaletteSelection() {
        const buttons = document.querySelectorAll('.palette-btn');
        buttons.forEach(btn => btn.classList.remove('active'));
    }

    populateColorInputs() {
        const mappings = {
            accentStart: '--accent-start',
            accentEnd: '--accent-end',
            accentAlt: '--accent-alt',
            bgStart: '--bg-gradient-start',
            bgEnd: '--bg-gradient-end',
            cardBg: '--card-bg'
        };

        Object.entries(mappings).forEach(([id, varName]) => {
            const input = document.getElementById(id);
            if (input) {
                const value = getComputedStyle(document.documentElement).getPropertyValue(varName);
                input.value = this.rgbToHex(value);
            }
        });
    }

    rgbToHex(rgb) {
        if (!rgb) return '#000000';
        const match = rgb.match(/rgba?\((\d+),?\s*(\d+),?\s*(\d+)/i);
        if (!match) return '#000000';
        
        return '#' + [match[1], match[2], match[3]]
            .map(x => parseInt(x).toString(16).padStart(2, '0'))
            .join('');
    }

    highlightPreset(preset) {
        const buttons = document.querySelectorAll('.preset-btn[data-preset]');
        buttons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.preset === preset);
        });
    }

    clearPresetSelection() {
        const buttons = document.querySelectorAll('.preset-btn[data-preset]');
        buttons.forEach(btn => btn.classList.remove('active'));
    }

    updateDateInputs() {
        const filter = this.dataManager.getCurrentFilter();
        const fromInput = document.getElementById('dateFrom');
        const toInput = document.getElementById('dateTo');

        if (fromInput && filter?.from) {
            fromInput.value = filter.from.toISOString().slice(0, 10);
        }
        if (toInput && filter?.to) {
            toInput.value = filter.to.toISOString().slice(0, 10);
        }
    }

    updateThemeControls(themeState) {
        // Update button labels based on current state
        const themeToggle = document.getElementById('toggleTheme');
        if (themeToggle) {
            themeToggle.textContent = themeState.mode === 'light' ? 
                'Váltás Sötét Témára' : 
                'Váltás Világos Témára';
        }

        const factsToggle = document.getElementById('toggleFacts');
        if (factsToggle) {
            factsToggle.textContent = themeState.factsMode ? 
                'Táblázat Stílus: Label ➜ Modern' : 
                'Táblázat Stílus: Modern ➜ Label';
        }

        const factsColorToggle = document.getElementById('toggleFactsColor');
        if (factsColorToggle) {
            factsColorToggle.textContent = `Label Színezés: ${themeState.factsColorMode ? 'BE' : 'KI'}`;
        }
    }
}

/**
 * Panel Class
 * Manages individual panel behavior
 */
class Panel {
    constructor(panelId, toggleId) {
        this.panel = document.getElementById(panelId);
        this.toggle = document.getElementById(toggleId);
        this.isOpenState = false;
        
        this.init();
    }

    init() {
        if (this.toggle) {
            this.toggle.addEventListener('click', () => {
                this.toggle();
            });
        }

        const closeBtn = this.panel?.querySelector('.panel-close, .close-btn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.close();
            });
        }
    }

    open() {
        if (this.panel) {
            this.panel.classList.add('open');
            this.isOpenState = true;
        }
    }

    close() {
        if (this.panel) {
            this.panel.classList.remove('open');
            this.isOpenState = false;
        }
    }

    toggle() {
        if (this.isOpenState) {
            this.close();
        } else {
            this.open();
        }
    }

    isOpen() {
        return this.isOpenState;
    }

    contains(element) {
        return this.panel?.contains(element) || false;
    }

    isToggleButton(element) {
        return this.toggle === element || this.toggle?.contains(element) || false;
    }
}

export { UIComponents, Panel };
