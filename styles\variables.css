/* ===================================================================
   CSS CUSTOM PROPERTIES (VARIABLES)
   Comprehensive color system and design tokens
   =================================================================== */

:root {
    /* ===============================
       CORE COLOR SYSTEM
       =============================== */
    
    /* Primary Brand Colors */
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    --color-primary-200: #bae6fd;
    --color-primary-300: #7dd3fc;
    --color-primary-400: #38bdf8;
    --color-primary-500: #0ea5e9;
    --color-primary-600: #0284c7;
    --color-primary-700: #0369a1;
    --color-primary-800: #075985;
    --color-primary-900: #0c4a6e;
    --color-primary-950: #082f49;

    /* Secondary Colors */
    --color-secondary-50: #fdf4ff;
    --color-secondary-100: #fae8ff;
    --color-secondary-200: #f5d0fe;
    --color-secondary-300: #f0abfc;
    --color-secondary-400: #e879f9;
    --color-secondary-500: #d946ef;
    --color-secondary-600: #c026d3;
    --color-secondary-700: #a21caf;
    --color-secondary-800: #86198f;
    --color-secondary-900: #701a75;
    --color-secondary-950: #4a044e;

    /* Accent Colors */
    --color-accent-50: #fff7ed;
    --color-accent-100: #ffedd5;
    --color-accent-200: #fed7aa;
    --color-accent-300: #fdba74;
    --color-accent-400: #fb923c;
    --color-accent-500: #f97316;
    --color-accent-600: #ea580c;
    --color-accent-700: #c2410c;
    --color-accent-800: #9a3412;
    --color-accent-900: #7c2d12;
    --color-accent-950: #431407;

    /* Neutral Colors */
    --color-neutral-50: #f8fafc;
    --color-neutral-100: #f1f5f9;
    --color-neutral-200: #e2e8f0;
    --color-neutral-300: #cbd5e1;
    --color-neutral-400: #94a3b8;
    --color-neutral-500: #64748b;
    --color-neutral-600: #475569;
    --color-neutral-700: #334155;
    --color-neutral-800: #1e293b;
    --color-neutral-900: #0f172a;
    --color-neutral-950: #020617;

    /* Semantic Colors */
    --color-success-50: #f0fdf4;
    --color-success-100: #dcfce7;
    --color-success-200: #bbf7d0;
    --color-success-300: #86efac;
    --color-success-400: #4ade80;
    --color-success-500: #22c55e;
    --color-success-600: #16a34a;
    --color-success-700: #15803d;
    --color-success-800: #166534;
    --color-success-900: #14532d;
    --color-success-950: #052e16;

    --color-warning-50: #fffbeb;
    --color-warning-100: #fef3c7;
    --color-warning-200: #fde68a;
    --color-warning-300: #fcd34d;
    --color-warning-400: #fbbf24;
    --color-warning-500: #f59e0b;
    --color-warning-600: #d97706;
    --color-warning-700: #b45309;
    --color-warning-800: #92400e;
    --color-warning-900: #78350f;
    --color-warning-950: #451a03;

    --color-error-50: #fef2f2;
    --color-error-100: #fee2e2;
    --color-error-200: #fecaca;
    --color-error-300: #fca5a5;
    --color-error-400: #f87171;
    --color-error-500: #ef4444;
    --color-error-600: #dc2626;
    --color-error-700: #b91c1c;
    --color-error-800: #991b1b;
    --color-error-900: #7f1d1d;
    --color-error-950: #450a0a;

    --color-info-50: #eff6ff;
    --color-info-100: #dbeafe;
    --color-info-200: #bfdbfe;
    --color-info-300: #93c5fd;
    --color-info-400: #60a5fa;
    --color-info-500: #3b82f6;
    --color-info-600: #2563eb;
    --color-info-700: #1d4ed8;
    --color-info-800: #1e40af;
    --color-info-900: #1e3a8a;
    --color-info-950: #172554;

    /* ===============================
       THEME SYSTEM - DARK (DEFAULT)
       =============================== */
    
    /* Background System */
    --bg-gradient-start: var(--color-neutral-900);
    --bg-gradient-end: var(--color-neutral-800);
    --bg-primary: var(--color-neutral-900);
    --bg-secondary: var(--color-neutral-800);
    --bg-tertiary: var(--color-neutral-700);

    /* Accent System */
    --accent-start: var(--color-primary-500);
    --accent-end: var(--color-secondary-500);
    --accent-alt: var(--color-accent-500);
    --accent-neutral: var(--color-neutral-400);
    --accent-warn: var(--color-warning-500);

    /* Card System */
    --card-bg: rgba(30, 41, 59, 0.85);
    --card-bg-solid: var(--color-neutral-800);
    --card-border: rgba(148, 163, 184, 0.15);
    --card-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.4);

    /* Text System */
    --text-color: var(--color-neutral-200);
    --text-dim: var(--color-neutral-400);
    --text-muted: var(--color-neutral-500);
    --text-inverse: var(--color-neutral-900);

    /* Interactive Elements */
    --table-row-hover: rgba(148, 163, 184, 0.06);
    --pill-bg: rgba(99, 102, 241, 0.15);
    --button-primary: linear-gradient(120deg, var(--accent-start), var(--accent-end));
    --button-secondary: rgba(255, 255, 255, 0.05);

    /* Status Colors */
    --positive: var(--color-success-500);
    --negative: var(--color-error-500);
    --neutral: var(--color-neutral-500);

    /* ===============================
       SPACING SYSTEM
       =============================== */
    
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */

    /* ===============================
       TYPOGRAPHY SYSTEM
       =============================== */
    
    --font-family-sans: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-family-mono: 'Consolas', 'Monaco', 'Courier New', monospace;

    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;

    /* ===============================
       BORDER RADIUS SYSTEM
       =============================== */
    
    --radius-none: 0;
    --radius-sm: 0.125rem;   /* 2px */
    --radius-base: 0.25rem;  /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-3xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;

    /* ===============================
       SHADOW SYSTEM
       =============================== */
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* ===============================
       ANIMATION SYSTEM
       =============================== */
    
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

    /* ===============================
       Z-INDEX SYSTEM
       =============================== */
    
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* ===============================
       BREAKPOINTS (for JS usage)
       =============================== */
    
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}
