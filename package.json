{"name": "financial-dashboard-v2", "version": "2.0.0", "description": "Advanced financial dashboard with modular architecture and comprehensive theming system", "main": "index.html", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "http-server . -p 3000 -c-1", "lint": "eslint js/**/*.js", "lint:fix": "eslint js/**/*.js --fix", "format": "prettier --write \"**/*.{js,css,html,md}\"", "test": "vitest", "test:ui": "vitest --ui", "docs": "jsdoc -c jsdoc.conf.json", "analyze": "vite-bundle-analyzer", "validate": "html-validate index.html", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./reports/lighthouse.html", "clean": "rimraf dist reports coverage docs/generated", "deploy": "npm run build && npm run lighthouse"}, "keywords": ["dashboard", "financial", "charts", "themes", "modular", "responsive", "accessibility"], "author": "Financial Dashboard Team", "license": "MIT", "devDependencies": {"@eslint/js": "^9.0.0", "@types/chart.js": "^2.9.41", "@vitejs/plugin-legacy": "^5.0.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jsdoc": "^48.0.0", "html-validate": "^8.0.0", "http-server": "^14.1.1", "jsdoc": "^4.0.2", "lighthouse": "^11.0.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "terser": "^5.19.0", "vite": "^5.0.0", "vite-bundle-analyzer": "^0.7.0", "vitest": "^1.0.0"}, "dependencies": {"chart.js": "^4.4.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/financial-dashboard-v2.git"}, "bugs": {"url": "https://github.com/your-org/financial-dashboard-v2/issues"}, "homepage": "https://your-org.github.io/financial-dashboard-v2"}