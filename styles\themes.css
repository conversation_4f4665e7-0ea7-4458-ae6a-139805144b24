/* ===================================================================
   THEME SYSTEM
   Multiple theme variations and color schemes
   =================================================================== */

/* ===============================
   LIGHT THEME
   =============================== */

.light-theme {
    /* Background System */
    --bg-gradient-start: #f5f7fa;
    --bg-gradient-end: #e4ecf7;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    /* Accent System */
    --accent-start: #667eea;
    --accent-end: #764ba2;
    --accent-alt: #8b5fbf;
    --accent-neutral: #64748b;
    --accent-warn: #f59e0b;

    /* Card System */
    --card-bg: rgba(255, 255, 255, 0.9);
    --card-bg-solid: #ffffff;
    --card-border: rgba(0, 0, 0, 0.06);
    --card-shadow: 0 20px 40px -10px rgba(31, 41, 55, 0.15);

    /* Text System */
    --text-color: #1e293b;
    --text-dim: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;

    /* Interactive Elements */
    --table-row-hover: #f1f5f9;
    --pill-bg: rgba(102, 126, 234, 0.12);
    --button-primary: linear-gradient(120deg, var(--accent-start), var(--accent-end));
    --button-secondary: rgba(0, 0, 0, 0.05);

    /* Status Colors */
    --positive: #10b981;
    --negative: #ef4444;
    --neutral: #6b7280;
}

/* ===============================
   FACTS MODE (NUTRITION LABEL STYLE)
   =============================== */

.facts-mode .data-table {
    background: #ffffff;
    color: #000000;
    box-shadow: 0 8px 30px -8px rgba(0, 0, 0, 0.55);
    border: 2px solid #000000;
    border-collapse: collapse;
}

.facts-mode .data-table thead {
    background: #ffffff;
    color: #000000;
}

.facts-mode .data-table th {
    font-size: var(--font-size-sm);
    padding: var(--space-sm);
    letter-spacing: var(--letter-spacing-wide);
    border-bottom: 6px solid #000000;
}

.facts-mode .data-table td {
    padding: var(--space-sm);
    font-size: var(--font-size-sm);
    border-bottom: 1px solid #000000;
}

.facts-mode .data-table tbody tr:hover {
    background: #f5f5f5;
}

.facts-mode .data-table tbody tr:last-child td {
    border-bottom: 3px solid #000000;
}

.facts-mode .percentage {
    background: #000000;
    color: #ffffff;
    font-weight: var(--font-weight-bold);
    letter-spacing: 0;
    border-radius: 0;
    padding: 2px var(--space-xs);
}

.facts-mode .table-container .card {
    background: #ffffff;
    border: 2px solid #000000;
    box-shadow: 0 12px 40px -10px rgba(0, 0, 0, 0.6);
}

.facts-mode .table-container h2 {
    color: #000000;
    background: linear-gradient(90deg, #ffffff, #dddddd);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
}

.facts-mode .table-container h2 .icon {
    background: linear-gradient(135deg, #111111, #222222);
}

.facts-mode .number {
    font-variant-numeric: tabular-nums;
}

/* ===============================
   FACTS COLOR MODE
   =============================== */

.facts-mode.facts-color .data-table td {
    transition: background-color var(--duration-normal), color var(--duration-normal);
}

/* Waste band classification via inset shadow */
.facts-mode.facts-color tr[data-waste-band="high"] {
    box-shadow: inset 4px 0 0 #dc2626;
}

.facts-mode.facts-color tr[data-waste-band="mid"] {
    box-shadow: inset 4px 0 0 #64748b;
}

.facts-mode.facts-color tr[data-waste-band="low"] {
    box-shadow: inset 4px 0 0 #059669;
}

.facts-mode.facts-color .delta-pill[data-delta="pos"] {
    background: #059669;
    color: #ffffff;
}

.facts-mode.facts-color .delta-pill[data-delta="neg"] {
    background: #dc2626;
    color: #ffffff;
}

.facts-mode.facts-color .delta-pill[data-delta="none"] {
    background: #111111;
    color: #ffffff;
    opacity: 0.6;
}

.facts-mode.facts-color .delta-pill {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: 2px var(--space-xs);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-wide);
}

.facts-mode.facts-color .delta-pill .arrow {
    font-size: var(--font-size-xs);
    line-height: 1;
}

.facts-mode.facts-color .w-cell {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
    min-width: 110px;
}

.facts-mode.facts-color .w-cell .w-amt {
    font-variant-numeric: tabular-nums;
    font-weight: var(--font-weight-semibold);
}

.facts-mode.facts-color .bar-wrap {
    position: relative;
    height: 6px;
    background: #000000;
    border-radius: var(--radius-base);
    overflow: hidden;
}

.facts-mode.facts-color .bar-fill {
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg, var(--accent-start), var(--accent-end));
    width: var(--w, 0%);
}

/* Alert highlighting */
.facts-mode.facts-color tr[data-alert="low-margin"] td:nth-child(3) .delta-pill {
    box-shadow: 0 0 0 2px #dc2626;
}

.facts-mode.facts-color tr[data-alert="high-waste"] td:nth-child(4) .delta-pill {
    box-shadow: 0 0 0 2px #dc2626;
}

/* ===============================
   DARK THEME REFINEMENTS
   =============================== */

body:not(.facts-mode):not(.light-theme) .data-table {
    background: linear-gradient(155deg, var(--card-bg), rgba(255, 255, 255, 0.02));
    box-shadow: var(--card-shadow);
    border: 1px solid var(--card-border);
}

body:not(.facts-mode):not(.light-theme) .data-table tbody tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

body:not(.facts-mode):not(.light-theme) .data-table th {
    color: #ffffff;
}

/* Dark mode pill override */
body:not(.light-theme):not(.facts-mode) .data-table .percentage {
    background: linear-gradient(120deg, var(--accent-start), var(--accent-end));
    color: #ffffff;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08);
}

/* ===============================
   PREDEFINED COLOR PALETTES
   =============================== */

/* Aurora Theme */
.theme-aurora {
    --accent-start: #0D3B66;
    --accent-end: #F4D35E;
    --accent-alt: #EE964B;
    --accent-neutral: #FAF0CA;
    --accent-warn: #F95738;
    --bg-gradient-start: #0D3B66;
    --bg-gradient-end: #0D3B66;
    --card-bg: rgba(13, 59, 102, 0.30);
}

/* Modern Tech Theme */
.theme-modern {
    --accent-start: #1B1F3B;
    --accent-end: #2E86AB;
    --accent-alt: #A23B72;
    --accent-neutral: #EDE7E3;
    --accent-warn: #F18F01;
    --bg-gradient-start: #1B1F3B;
    --bg-gradient-end: #2E86AB;
    --card-bg: rgba(27, 31, 59, 0.35);
}

/* Fresh Mint Theme */
.theme-mint {
    --accent-start: #2D6A4F;
    --accent-end: #40916C;
    --accent-alt: #95D5B2;
    --accent-neutral: #D8F3DC;
    --accent-warn: #FFB703;
    --bg-gradient-start: #2D6A4F;
    --bg-gradient-end: #40916C;
    --card-bg: rgba(45, 106, 79, 0.30);
}

/* Sunset Calm Theme */
.theme-sunset {
    --accent-start: #22223B;
    --accent-end: #4A4E69;
    --accent-alt: #9A8C98;
    --accent-neutral: #C9ADA7;
    --accent-warn: #F28482;
    --bg-gradient-start: #22223B;
    --bg-gradient-end: #4A4E69;
    --card-bg: rgba(34, 34, 59, 0.32);
}

/* Neon Contrast Theme */
.theme-neon {
    --accent-start: #0A0221;
    --accent-end: #63ADF2;
    --accent-alt: #FE5F55;
    --accent-neutral: #FFC145;
    --accent-warn: #3E8989;
    --bg-gradient-start: #0A0221;
    --bg-gradient-end: #0A0221;
    --card-bg: rgba(10, 2, 33, 0.36);
}

/* Carbon Citrus Theme */
.theme-carbon {
    --accent-start: #0F0F0F;
    --accent-end: #FF6B35;
    --accent-alt: #4ECDC4;
    --accent-neutral: #F7C59F;
    --accent-warn: #FFB400;
    --bg-gradient-start: #0F0F0F;
    --bg-gradient-end: #1F2933;
    --card-bg: rgba(15, 15, 15, 0.40);
}

/* Data Pastel Theme */
.theme-pastel {
    --accent-start: #264653;
    --accent-end: #2A9D8F;
    --accent-alt: #E9C46A;
    --accent-neutral: #F4A261;
    --accent-warn: #E76F51;
    --bg-gradient-start: #264653;
    --bg-gradient-end: #2A9D8F;
    --card-bg: rgba(38, 70, 83, 0.33);
}
