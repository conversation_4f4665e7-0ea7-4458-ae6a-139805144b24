<!DOCTYPE html>
<html lang="hu">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pénzügyi Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        :root {
            /* Base dark theme variables (can be changed dynamically) */
            --bg-gradient-start: #0f172a; /* slate-900 */
            --bg-gradient-end: #1e293b;   /* slate-800 */
            --accent-start: #6366f1;      /* indigo-500 */
            --accent-end: #8b5cf6;        /* violet-500 */
            --accent-alt: #a855f7;        /* fuchsia/violet blend */
            --card-bg: rgba(30, 41, 59, 0.85);
            --card-bg-solid: #1e293b;
            --card-border: rgba(148, 163, 184, 0.15);
            --text-color: #e2e8f0;        /* slate-200 */
            --text-dim: #94a3b8;          /* slate-400 */
            --table-row-hover: rgba(148, 163, 184, 0.06);
            --pill-bg: rgba(99, 102, 241, 0.15);
            --positive: #10b981;
            --negative: #ef4444;
            --shadow-color: 0 20px 40px -10px rgba(0,0,0,0.4);
        }

        .light-theme {
            --bg-gradient-start: #f5f7fa;
            --bg-gradient-end: #e4ecf7;
            --accent-start: #667eea;
            --accent-end: #764ba2;
            --accent-alt: #8b5fbf;
            --card-bg: rgba(255, 255, 255, 0.9);
            --card-bg-solid: #ffffff;
            --card-border: rgba(0,0,0,0.06);
            --text-color: #1e293b;
            --text-dim: #64748b;
            --table-row-hover: #f1f5f9;
            --pill-bg: rgba(102, 126, 234, 0.12);
            --shadow-color: 0 20px 40px -10px rgba(31,41,55,0.15);
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: radial-gradient(circle at 25% 20%, var(--bg-gradient-start), var(--bg-gradient-end));
            min-height: 100vh;
            padding: 20px 20px 80px;
            color: var(--text-color);
            transition: background 0.8s ease, color 0.4s ease;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: linear-gradient(145deg, rgba(255,255,255,0.04), rgba(255,255,255,0));
            backdrop-filter: blur(22px) saturate(140%);
            border: 1px solid var(--card-border);
            border-radius: 24px;
            padding: 30px clamp(20px, 4vw, 50px);
            margin-bottom: 32px;
            box-shadow: var(--shadow-color);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header:before {
            content: "";
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at 70% 30%, rgba(255,255,255,0.08), transparent 60%);
            pointer-events: none;
        }

        .header h1 {
            font-size: clamp(2rem, 5vw, 2.8rem);
            margin-bottom: 10px;
            background: linear-gradient(90deg, var(--accent-start), var(--accent-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 1px;
        }

        .header p {
            color: var(--text-dim);
            font-size: 1.05rem;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: linear-gradient(155deg, var(--card-bg), rgba(255,255,255,0.02));
            backdrop-filter: blur(18px) saturate(160%);
            border: 1px solid var(--card-border);
            border-radius: 22px;
            padding: 28px 30px 30px;
            box-shadow: var(--shadow-color);
            transition: transform 0.35s ease, box-shadow 0.35s ease, border-color 0.4s;
            position: relative;
            overflow: hidden;
        }

        .card:before {
            content: "";
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at 80% 20%, rgba(255,255,255,0.06), transparent 65%);
            opacity: 0;
            transition: 0.5s ease;
            pointer-events: none;
        }

        .card:hover {
            transform: translateY(-6px) scale(1.01);
            box-shadow: 0 32px 70px -12px rgba(0,0,0,0.55);
            border-color: rgba(255,255,255,0.18);
        }

        .card:hover:before { opacity: 1; }

        .card h2 {
            color: var(--text-color);
            margin-bottom: 16px;
            font-size: 1.35rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            letter-spacing: .5px;
        }

        .icon {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, var(--accent-start), var(--accent-end));
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 15px;
            box-shadow: 0 4px 10px -2px rgba(0,0,0,0.4);
        }

        .table-container {
            grid-column: 1 / -1;
        }

        .data-table {
                width: 100%;
                border-collapse: separate;
                border-spacing: 0;
                border-radius: 18px;
                overflow: hidden;
                box-shadow: var(--shadow-color);
                background: linear-gradient(160deg, var(--card-bg-solid), rgba(15,23,42,0.9));
                border: 1px solid var(--card-border);
                table-layout: fixed;
        }

        .data-table thead {
            background: linear-gradient(90deg, var(--accent-start), var(--accent-end));
            color: #fff;
        }

        .data-table th {
            padding: 14px 14px;
            text-align: left;
            font-weight: 600;
            font-size: .9rem;
            letter-spacing: .5px;
            white-space: nowrap;
        }

        .data-table td {
            padding: 10px 14px;
            border-bottom: 1px solid rgba(148,163,184,0.12);
            transition: background-color 0.35s ease, color 0.35s;
            vertical-align: middle;
            font-size:.8rem;
        }

        .data-table tbody tr:hover {
            background-color: var(--table-row-hover);
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

    .number { text-align: right; font-weight: 600; }

    .positive { color: var(--positive); }
    .negative { color: var(--negative); }

        .percentage {
            background: var(--pill-bg);
            padding: 4px 10px;
            border-radius: 30px;
            font-size: 0.8rem;
            font-weight: 600;
            letter-spacing: .5px;
            color: var(--accent-start);
        }
        /* Dark mode pill override */
        body:not(.light-theme):not(.facts-mode) .data-table .percentage { 
            background: linear-gradient(120deg,var(--accent-start),var(--accent-end)); 
            color:#fff; 
            box-shadow:0 0 0 1px rgba(255,255,255,0.08); 
        }
        /* Always black background for Árrés % (col 3) and Selejt % (col 4) value pills */
        .data-table td:nth-child(3) .percentage,
        .data-table td:nth-child(4) .percentage,
        body.facts-mode .data-table td:nth-child(3) .percentage,
        body.facts-mode .data-table td:nth-child(4) .percentage { 
            background:#000 !important; 
            color:#fff !important; 
            box-shadow:0 0 0 1px rgba(255,255,255,0.15); 
        }
        /* Hover state keep black */
        .data-table tbody tr:hover td:nth-child(3) .percentage,
        .data-table tbody tr:hover td:nth-child(4) .percentage { background:#000 !important; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(150deg, var(--card-bg), rgba(255,255,255,0.03));
            backdrop-filter: blur(16px) saturate(160%);
            border: 1px solid var(--card-border);
            border-radius: 18px;
            padding: 22px 20px 20px;
            text-align: center;
            box-shadow: 0 12px 40px -12px rgba(0,0,0,0.5);
            transition: transform 0.35s ease, border-color .4s;
            position: relative;
            overflow: hidden;
        }

        .stat-card:before {
            content: "";
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at 80% 25%, rgba(255,255,255,0.07), transparent 62%);
            opacity: 0;
            transition: .45s ease;
        }

    .stat-card:hover { transform: translateY(-4px); border-color: rgba(255,255,255,0.18); }
    .stat-card:hover:before { opacity: 1; }

        .stat-value {
            font-size: 1.9rem;
            font-weight: 700;
            margin-bottom: 6px;
            background: linear-gradient(120deg, var(--accent-start), var(--accent-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: .5px;
        }

        .stat-label {
            color: var(--text-dim);
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin-top: 20px;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }

        .filter-btn {
            padding: 10px 18px;
            border: 1px solid var(--card-border);
            border-radius: 30px;
            background: linear-gradient(120deg, var(--accent-start), var(--accent-end));
            color: #fff;
            cursor: pointer;
            transition: all 0.35s ease;
            font-weight: 600;
            font-size: .85rem;
            letter-spacing: .5px;
            position: relative;
        }
        .filter-btn:hover { transform: translateY(-3px); box-shadow: 0 10px 25px -6px rgba(0,0,0,0.55); }
        .filter-btn.active { background: linear-gradient(120deg, var(--accent-end), var(--accent-start)); }

        /* Date filter + presets */
        .preset-btn { 
            padding:6px 10px; 
            border-radius:20px; 
            border:1px solid var(--card-border); 
            background:var(--card-bg); 
            color:var(--text-dim); 
            cursor:pointer; 
            font-size:.6rem; 
            font-weight:600; 
            letter-spacing:1px; 
            text-transform:uppercase; 
            transition:.3s ease; 
        }
        .preset-btn:hover { background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); color:#fff; border-color:transparent; }
        .preset-btn.active { background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); color:#fff; }

        @media (max-width: 900px) {
            .dashboard { grid-template-columns: 1fr; }
            .controls-panel { width: calc(100% - 32px); left: 16px; right: 16px; }
            .theme-toggle { position: fixed; bottom: 18px; left: 18px; }
        }

        .loading {
            display: inline-block;
            width: 20px; height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            border-top-color: var(--accent-start);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin { to { transform: rotate(360deg); } }

        /* Customization Panel */
    .controls-panel-toggle, .data-panel-toggle {
            position: fixed;
            top: 18px; right: 18px;
            z-index: 999;
            background: linear-gradient(135deg, var(--accent-start), var(--accent-end));
            color: #fff;
            border: none;
            padding: 10px 16px;
            font-weight: 600;
            border-radius: 14px;
            cursor: pointer;
            box-shadow: 0 12px 32px -8px rgba(0,0,0,0.6);
            display: flex; align-items: center; gap: 6px;
            letter-spacing: .5px;
            transition: .35s ease;
        }
    .controls-panel-toggle { right: 18px; }
    .data-panel-toggle { right: 150px; }
    .controls-panel-toggle:hover, .data-panel-toggle:hover { transform: translateY(-3px); }
    .data-panel-toggle.active-filter { box-shadow: 0 0 0 2px var(--accent-end), 0 0 0 6px rgba(0,0,0,0.4); }

        .controls-panel {
            position: fixed;
            top: 70px; right: 18px;
            width: 310px;
            max-height: calc(100vh - 90px);
            overflow-y: auto;
            background: linear-gradient(150deg, rgba(15,23,42,0.92), rgba(30,41,59,0.92));
            backdrop-filter: blur(20px) saturate(160%);
            border: 1px solid var(--card-border);
            border-radius: 20px;
            padding: 20px 20px 28px;
            box-shadow: 0 20px 60px -15px rgba(0,0,0,0.65);
            z-index: 990;
            color: var(--text-color);
            display: none;
        }
    .controls-panel.open { display: block; animation: panelEnter .5s ease; }
    .data-panel { position: fixed; top: 70px; right: 160px; width: 320px; max-height: calc(100vh - 90px); overflow-y:auto; background: linear-gradient(150deg, rgba(15,23,42,0.92), rgba(30,41,59,0.92)); backdrop-filter: blur(20px) saturate(160%); border:1px solid var(--card-border); border-radius:20px; padding:20px 20px 24px; box-shadow:0 20px 60px -15px rgba(0,0,0,0.65); z-index:989; display:none; }
    .data-panel.open { display:block; animation: panelEnter .45s ease; }
    .data-panel h3 { font-size:1rem; margin:0 0 14px; letter-spacing:1px; text-transform:uppercase; color:var(--text-dim); display:flex; align-items:center; gap:8px; }
    .data-panel .sec-label { font-size:.6rem; text-transform:uppercase; letter-spacing:1.5px; color:var(--text-dim); display:block; margin-bottom:6px; }
    .data-panel .range-row { display:flex; gap:8px; flex-wrap:wrap; margin-bottom:14px; }
    .data-panel input[type=date] { flex:1; padding:8px 10px; border-radius:10px; border:1px solid var(--card-border); background:var(--card-bg); color:var(--text-color); font-size:.7rem; }
    .data-panel .preset-grid { display:flex; flex-wrap:wrap; gap:6px; margin-bottom:12px; }
    .data-panel .panel-actions { display:flex; gap:8px; margin:6px 0 12px; }
    .data-panel .panel-actions .primary { flex:1; background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); color:#fff; border:none; padding:8px 12px; border-radius:10px; font-weight:600; cursor:pointer; font-size:.7rem; letter-spacing:1px; }
    .data-panel .panel-actions .ghost { background:rgba(255,255,255,0.05); border:1px solid var(--card-border); color:var(--text-dim); padding:8px 12px; border-radius:10px; font-weight:600; cursor:pointer; font-size:.65rem; letter-spacing:1px; }
    .data-panel .panel-badge { margin-bottom:14px; }
    .data-panel #dateFilterBadge { display:none; background:var(--pill-bg); color:var(--accent-start); padding:5px 10px; border-radius:40px; font-size:.55rem; letter-spacing:1px; box-shadow:0 0 0 1px var(--card-border); }
    .data-panel input[type=text] { width:100%; padding:10px 14px; border-radius:14px; border:1px solid var(--card-border); background:var(--card-bg); color:var(--text-color); font-size:.75rem; }
    .data-panel .close-btn { position:absolute; top:10px; right:10px; background:rgba(255,255,255,0.07); border:1px solid var(--card-border); width:28px; height:28px; border-radius:50%; cursor:pointer; color:var(--text-dim); font-weight:700; display:flex; align-items:center; justify-content:center; }
    .data-panel .close-btn:hover { background:rgba(255,255,255,0.15); }
        @keyframes panelEnter { from { opacity:0; transform: translateY(-8px); } to { opacity:1; transform: translateY(0);} }
        .controls-panel h3 { font-size: 1rem; margin: 0 0 14px; letter-spacing: 1px; text-transform: uppercase; color: var(--text-dim); }
        .color-row { display: flex; align-items: center; justify-content: space-between; margin-bottom: 14px; gap: 8px; }
        .color-row label { flex: 1; font-size: .75rem; letter-spacing: 1.5px; text-transform: uppercase; color: var(--text-dim); }
        .color-row input[type="color"] { width: 46px; height: 36px; border: 1px solid var(--card-border); border-radius: 10px; background: transparent; cursor: pointer; padding: 0; }
        .small-note { font-size: .65rem; opacity: .7; line-height: 1.4; margin-top: 6px; }
        .panel-divider { height: 1px; background: linear-gradient(90deg, transparent, var(--card-border), transparent); margin: 18px 0; }
        .theme-toggle { margin-top: 4px; width: 100%; padding: 10px 14px; background: linear-gradient(120deg, var(--accent-start), var(--accent-end)); border: none; border-radius: 12px; color: #fff; cursor: pointer; font-weight: 600; letter-spacing: .5px; box-shadow: 0 8px 24px -8px rgba(0,0,0,0.5); transition: .35s ease; }
        .theme-toggle:hover { transform: translateY(-3px); }
        .reset-btn { width: 100%; margin-top: 10px; background: linear-gradient(120deg, #334155, #475569); border: none; padding: 9px 14px; border-radius: 12px; color: #e2e8f0; font-weight: 600; letter-spacing: .5px; cursor: pointer; transition: .35s ease; }
        .reset-btn:hover { background: linear-gradient(120deg,#475569,#334155); }
        .tiny { font-size: .6rem; letter-spacing: 1px; text-transform: uppercase; opacity: .55; margin-top: 14px; text-align: center; }
        ::-webkit-scrollbar { width: 10px; }
        ::-webkit-scrollbar-track { background: rgba(255,255,255,0.03); }
        ::-webkit-scrollbar-thumb { background: linear-gradient(var(--accent-start), var(--accent-end)); border-radius: 20px; border: 2px solid rgba(0,0,0,0.4); }
        ::selection { background: var(--accent-start); color: #fff; }

    /* Nutrition Facts inspired style (toggleable) */
    body.facts-mode .table-container h2 .icon { background: linear-gradient(135deg,#111,#222); }
    body.facts-mode .table-container h2 { background: linear-gradient(90deg,#fff,#ddd); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent; filter: drop-shadow(0 2px 6px rgba(0,0,0,0.4)); }
    body.facts-mode .data-table { background: #fff; color: #000; box-shadow: 0 8px 30px -8px rgba(0,0,0,0.55); border: 2px solid #000; }
    body.facts-mode.light-theme .data-table { background: #fff; }
    body.facts-mode .data-table thead { background: #fff; color: #000; }
    body.facts-mode .data-table th { font-size: .85rem; padding: 10px 10px; letter-spacing: .5px; }
    body.facts-mode .data-table td { padding: 8px 10px; font-size: .8rem; border-bottom: 1px solid #000; }
    body.facts-mode .data-table tbody tr:hover { background: #f5f5f5; }
    body.facts-mode .data-table tbody tr:last-child td { border-bottom: 3px solid #000; }
    body.facts-mode .data-table thead tr:first-child th { border-bottom: 6px solid #000; }
    body.facts-mode .data-table { border-collapse: collapse; }
    body.facts-mode .percentage { background: #000; color: #fff; font-weight: 700; letter-spacing: 0; border-radius: 0; padding: 2px 6px; }
    body.facts-mode .table-container .controls input { border: 2px solid #000 !important; background: #fff !important; color: #000 !important; }
    body.facts-mode .table-container { position: relative; }
    body.facts-mode .table-container:before { content:""; position: absolute; inset: 0; pointer-events: none; border-radius: 22px; }
    body.facts-mode .table-container .card { background: #fff; border: 2px solid #000; box-shadow: 0 12px 40px -10px rgba(0,0,0,0.6); }
    body.facts-mode .table-container .card h2 { color: #000; -webkit-text-fill-color: initial; }
    body.facts-mode .number { font-variant-numeric: tabular-nums; }
    body.facts-mode .percentage { font-variant-numeric: tabular-nums; }
    body.facts-mode .data-table th:first-child, body.facts-mode .data-table td:first-child { width: 38%; }
    .facts-toggle-btn { width: 100%; margin-top: 10px; background: linear-gradient(120deg,#111,#333); border: 1px solid #444; color: #f8fafc; padding: 9px 14px; border-radius: 12px; font-weight: 600; letter-spacing: .5px; cursor: pointer; transition: .35s ease; }
    .facts-toggle-btn:hover { background: linear-gradient(120deg,#000,#222); }
    /* New layout v2: flex row for table + yearly chart, full-width trend below */
    .layout-row { display: flex; gap: 30px; margin-bottom: 34px; align-items: stretch; }
    .table-card { display: flex; flex-direction: column; flex: 2 1 0; min-width: 0; }
    .table-card .data-table-wrapper { border-radius: 14px; }
    .table-card .data-table { width: 100%; }
    .yearly-card { flex: 1 1 360px; display: flex; flex-direction: column; }
    .yearly-card .chart-container { flex: 1; min-height: 100%; height: 100%; }
    /* Explicit unified height */
    /* Let table grow naturally; only enforce min height for yearly chart */
    .layout-row .yearly-card { min-height: 480px; }
    .yearly-card .chart-container { min-height: 440px; }
    .trend-wide { margin-bottom: 40px; }
    /* Shorter viewports adjust */
    @media (max-height: 900px){ .layout-row .yearly-card { min-height: 420px; } .yearly-card .chart-container { min-height: 380px; } }
    @media (max-height: 760px){ .layout-row .yearly-card { min-height: 360px; } .yearly-card .chart-container { min-height: 320px; } }
    @media (max-width: 1200px){ .layout-row { flex-direction: column; } .layout-row .yearly-card { min-height: 360px; } }
    @media (max-width: 600px){ .table-card .data-table th, .table-card .data-table td { font-size: .72rem; padding: 6px 6px; } }

    /* Mini charts section */
    .mini-charts-row { display: grid; grid-template-columns: repeat(auto-fit,minmax(260px,1fr)); gap: 24px; margin-top: 50px; }
    .mini-column { display:flex; flex-direction:column; gap:18px; flex:1 1 320px; }
    .two-col-row { display:grid; grid-template-columns: repeat(auto-fit,minmax(380px,1fr)); gap:30px; margin-top:40px; }
    .mini-card { background: linear-gradient(150deg, var(--card-bg), rgba(255,255,255,0.03)); backdrop-filter: blur(14px) saturate(160%); border: 1px solid var(--card-border); border-radius: 18px; padding: 16px 18px 14px; position: relative; box-shadow: 0 10px 28px -10px rgba(0,0,0,0.55); display:flex; flex-direction:column; }
    .mini-card h4 { margin: 0 0 8px; font-size: .8rem; letter-spacing: 1.5px; font-weight: 600; text-transform: uppercase; color: var(--text-dim); display:flex; align-items:center; gap:6px; }
    .mini-card canvas { width: 100% !important; height: 90px !important; }
    .mini-card .value-badge { position:absolute; top:10px; right:12px; font-size:.65rem; font-weight:600; background: var(--pill-bg); color: var(--accent-start); padding:4px 8px; border-radius: 30px; letter-spacing:.5px; }
    @media (max-width: 700px){ .mini-card canvas { height: 70px !important; } }

    /* Yearly info panel */
    .year-info-panel { margin-top:14px; background: linear-gradient(140deg, rgba(255,255,255,0.04), rgba(255,255,255,0.02)); border:1px solid var(--card-border); border-radius:14px; padding:14px 16px 12px; font-size:.7rem; display:flex; flex-direction:column; gap:10px; position:relative; overflow:hidden; }
    .year-info-panel:before { content:""; position:absolute; inset:0; background: radial-gradient(circle at 85% 15%, rgba(255,255,255,0.10), transparent 65%); pointer-events:none; }
    .year-info-header { display:flex; justify-content:space-between; align-items:center; font-size:.75rem; letter-spacing:1px; text-transform:uppercase; color:var(--text-dim); }
    .year-info-grid { display:grid; grid-template-columns:repeat(3,1fr); gap:10px; }
    .info-box { background:rgba(255,255,255,0.04); border:1px solid var(--card-border); border-radius:10px; padding:8px 8px 6px; display:flex; flex-direction:column; gap:2px; }
    .info-label { font-size:.55rem; letter-spacing:1px; text-transform:uppercase; color:var(--text-dim); font-weight:600; }
    .info-value { font-size:.75rem; font-weight:600; line-height:1.1; }
    .delta { font-size:.6rem; font-weight:600; display:flex; gap:4px; align-items:center; }
    .delta.up { color: var(--positive); }
    .delta.down { color: var(--negative); }
    .mode-switch { display:flex; gap:6px; }
    .mode-btn { flex:1; background:rgba(255,255,255,0.05); border:1px solid var(--card-border); color:var(--text-dim); font-size:.6rem; padding:5px 6px; border-radius:8px; cursor:pointer; font-weight:600; letter-spacing:.5px; transition:.3s; }
    .mode-btn.active { background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); color:#fff; border-color:transparent; }
    .info-footer { font-size:.55rem; letter-spacing:.5px; color:var(--text-dim); display:flex; flex-wrap:wrap; gap:10px; }
    @media (max-width: 700px){ .year-info-grid { grid-template-columns:repeat(2,1fr); } }

    /* Yearly summary list */
    .year-summary-list { display:flex; flex-direction:column; gap:14px; margin-top:8px; max-height:420px; overflow:auto; padding-right:4px; }
    .year-summary-item { position:relative; border:1px solid var(--card-border); border-radius:16px; padding:14px 16px 12px; background:linear-gradient(135deg, rgba(255,255,255,0.04), rgba(255,255,255,0.02)); backdrop-filter:blur(10px) saturate(150%); box-shadow:0 6px 18px -8px rgba(0,0,0,0.45); }
    .year-summary-item:before { content:""; position:absolute; inset:0; border-radius:inherit; background:radial-gradient(circle at 85% 20%, rgba(255,255,255,0.08), transparent 65%); pointer-events:none; }
    .ysi-header { display:flex; justify-content:space-between; align-items:center; margin-bottom:10px; }
    .ysi-year { font-size:.8rem; font-weight:700; letter-spacing:1px; background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); -webkit-background-clip:text; background-clip:text; -webkit-text-fill-color:transparent; color:transparent; }
    .ysi-index-badge { font-size:.55rem; font-weight:600; letter-spacing:.5px; padding:4px 8px; border-radius:40px; background:var(--pill-bg); color:var(--accent-start); }
    .ysi-grid { display:grid; grid-template-columns:repeat(3,1fr); gap:12px; }
    .ysi-block { display:flex; flex-direction:column; gap:2px; }
    .ysi-block label { font-size:.5rem; font-weight:600; letter-spacing:1px; text-transform:uppercase; color:var(--text-dim); }
    .ysi-val { font-size:.75rem; font-weight:600; line-height:1.1; }
    .ysi-idx { font-size:.55rem; font-weight:600; letter-spacing:.5px; color:var(--text-dim); }
    .ysi-delta { font-size:.55rem; font-weight:600; }
    .ysi-delta.up { color:var(--positive); }
    .ysi-delta.down { color:var(--negative); }
    .placeholder-card { position:relative; }
    .placeholder-card h2 { margin-bottom:12px; }
    .year-summary-heading { font-size:.65rem; letter-spacing:1px; text-transform:uppercase; color:var(--text-dim); margin-bottom:4px; font-weight:600; }
    .year-summary-list::-webkit-scrollbar { width:8px; }
    .year-summary-list::-webkit-scrollbar-track { background:rgba(255,255,255,0.04); }
    .year-summary-list::-webkit-scrollbar-thumb { background:linear-gradient(var(--accent-start),var(--accent-end)); border-radius:20px; }
    @media (max-width: 800px){ .ysi-grid { grid-template-columns:repeat(2,1fr); } }

    /* Variant switcher and variants */
    .variant-switch { display:flex; gap:8px; margin:4px 0 6px; }
    .variant-btn { flex:1; background:rgba(255,255,255,0.05); border:1px solid var(--card-border); color:var(--text-dim); font-size:.6rem; padding:8px 8px; border-radius:10px; cursor:pointer; font-weight:600; letter-spacing:.5px; transition:.3s; }
    .variant-btn.active { background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); color:#fff; border-color:transparent; }
    .variant-content { min-height:260px; }
    /* Variant 1 list */
    .variant1-list { display:flex; flex-direction:column; gap:4px; font-size:.68rem; }
    .variant1-list .v1-row { display:grid; grid-template-columns: 60px repeat(8,1fr); gap:6px; align-items:center; padding:6px 10px; background:rgba(255,255,255,0.04); border:1px solid var(--card-border); border-radius:10px; }
    .variant1-list .v1-head { background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); color:#fff; font-weight:600; letter-spacing:.5px; position:sticky; top:0; z-index:1; }
    .variant1-list .v1-row div { overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
    /* Variant 2 cards */
    .v2-grid { display:grid; grid-template-columns: repeat(auto-fill,minmax(200px,1fr)); gap:14px; }
    .v2-card { position:relative; border:1px solid var(--card-border); background:linear-gradient(140deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02)); border-radius:16px; padding:12px 14px 10px; display:flex; flex-direction:column; gap:10px; font-size:.62rem; min-width:0; }
    .v2-head { display:flex; justify-content:space-between; align-items:center; font-size:.65rem; font-weight:600; letter-spacing:.5px; }
    .v2-badge { background:var(--pill-bg); color:var(--accent-start); padding:4px 8px; border-radius:30px; font-size:.5rem; font-weight:600; }
    .v2-section label { font-size:.55rem; text-transform:uppercase; letter-spacing:1px; color:var(--text-dim); font-weight:600; }
    .v2-val { font-size:.75rem; font-weight:600; }
    .v2-sub { font-size:.5rem; color:var(--text-dim); }
    .v2-bars { height:6px; background:rgba(255,255,255,0.06); border-radius:4px; overflow:hidden; margin:6px 0 4px; }
    .v2-bars.small { height:4px; }
    .v2-bar { height:100%; width:var(--p); background:linear-gradient(90deg,var(--accent-start),var(--accent-end)); border-radius:inherit; }
    .v2-delta { font-size:.55rem; font-weight:600; }
    .v2-split { display:grid; grid-template-columns:1fr 1fr; gap:10px; }
    .v2-val-sm { font-size:.65rem; font-weight:600; }
    .v2-val-sm .muted { font-size:.55rem; color:var(--text-dim); font-weight:500; }
    .v2-footer { margin-top:auto; font-size:.48rem; letter-spacing:.5px; color:var(--text-dim); }
    /* Variant 3 matrix */
    .v3-matrix { display:flex; flex-direction:column; gap:4px; font-size:.62rem; }
    .v3-row { display:grid; grid-template-columns:60px repeat(8,1fr); gap:6px; align-items:center; padding:6px 10px; background:rgba(255,255,255,0.04); border:1px solid var(--card-border); border-radius:10px; }
    .v3-head { background:linear-gradient(120deg,var(--accent-start),var(--accent-end)); color:#fff; font-weight:600; letter-spacing:.5px; position:sticky; top:0; z-index:1; }
    .v3-row div { overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
    @media (max-width: 900px){
        .variant1-list .v1-row, .v3-row { grid-template-columns: 50px repeat(8,120px); overflow:auto; }
    }
    /* Positive / Negative coloring */
    .pos { color: var(--positive); font-weight:600; }
    .neg { color: var(--negative); font-weight:600; }
    /* ================= FACTS-MODE COLORIZATION EXTENSIONS ================= */
    .facts-mode.facts-color .data-table td { transition: background-color .35s, color .35s; }
    /* Waste band via inset shadow (no layout shift) */
    .facts-mode.facts-color tr[data-waste-band="high"] { box-shadow: inset 4px 0 0 #dc2626; }
    .facts-mode.facts-color tr[data-waste-band="mid"]  { box-shadow: inset 4px 0 0 #64748b; }
    .facts-mode.facts-color tr[data-waste-band="low"]  { box-shadow: inset 4px 0 0 #059669; }
    .facts-mode.facts-color .delta-pill[data-delta="pos"] { background:#059669; color:#fff; }
    .facts-mode.facts-color .delta-pill[data-delta="neg"] { background:#dc2626; color:#fff; }
    .facts-mode.facts-color .delta-pill[data-delta="none"] { background:#111; color:#fff; opacity:.6; }
    .facts-mode.facts-color .delta-pill { display:inline-flex; align-items:center; gap:4px; padding:2px 6px; border-radius:14px; font-size:.65rem; font-weight:600; letter-spacing:.5px; }
    .facts-mode.facts-color .delta-pill .arrow { font-size:.55rem; line-height:1; }
    .facts-mode.facts-color .w-cell { display:flex; flex-direction:column; gap:4px; min-width:110px; }
    .facts-mode.facts-color .w-cell .w-amt { font-variant-numeric:tabular-nums; font-weight:600; }
    .facts-mode.facts-color .bar-wrap { position:relative; height:6px; background:#000; border-radius:4px; overflow:hidden; }
    .facts-mode.facts-color .bar-fill { position:absolute; inset:0; background:linear-gradient(90deg,var(--accent-start),var(--accent-end)); width:var(--w,0%); }
    .facts-mode.facts-color tr[data-alert="low-margin"] td:nth-child(3) .delta-pill { box-shadow:0 0 0 2px #dc2626; }
    .facts-mode.facts-color tr[data-alert="high-waste"] td:nth-child(4) .delta-pill { box-shadow:0 0 0 2px #dc2626; }
    .facts-color-btn { width:100%; margin-top:10px; background:linear-gradient(120deg,#334155,#475569); border:1px solid #475569; color:#e2e8f0; padding:8px 14px; border-radius:12px; font-weight:600; cursor:pointer; font-size:.75rem; letter-spacing:.5px; transition:.35s; }
    .facts-color-btn:hover { background:linear-gradient(120deg,#475569,#334155); }
    @media print { .facts-mode.facts-color .bar-wrap { display:none; } }
    /* Legend + change column */
    .legend-card { background:linear-gradient(150deg, var(--card-bg), rgba(255,255,255,0.03)); backdrop-filter:blur(14px) saturate(160%); border:1px solid var(--card-border); border-radius:18px; padding:14px 16px 12px; box-shadow:0 10px 28px -10px rgba(0,0,0,0.55); display:flex; flex-direction:column; gap:10px; }
    .legend-card h4 { margin:0; font-size:.7rem; letter-spacing:1.5px; text-transform:uppercase; color:var(--text-dim); font-weight:600; }
    .legend-grid { display:grid; grid-template-columns:repeat(auto-fill,minmax(140px,1fr)); gap:6px 14px; }
    .legend-item { display:flex; align-items:center; gap:8px; font-size:.6rem; color:var(--text-dim); line-height:1.2; }
    .legend-swatch { width:14px; height:14px; border-radius:4px; background:var(--accent-start); box-shadow:0 0 0 1px rgba(255,255,255,0.08); }
    .legend-swatch.line { height:4px; border-radius:2px; }
    .legend-swatch.margin { background:var(--accent-end); }
    .legend-swatch.wastepct { background:var(--accent-alt); }
    .legend-swatch.wastebar { background:linear-gradient(90deg,var(--accent-start),var(--accent-end)); height:8px; border-radius:3px; }
    .legend-swatch.pos { background:#059669; }
    .legend-swatch.neg { background:#dc2626; }
    .legend-mini { font-size:.5rem; opacity:.7; }
    /* Change column cell */
    .change-col { font-size:.62rem; line-height:1.15; display:flex; flex-direction:column; gap:4px; font-variant-numeric:tabular-nums; }
    .delta-line { display:flex; align-items:center; gap:4px; }
    .delta-metric { font-size:.5rem; font-weight:700; letter-spacing:.5px; }
    .delta-arrow { font-size:.55rem; line-height:1; }
    .delta-val { font-weight:600; font-variant-numeric:tabular-nums; }
    /* New colored background style */
    .change-col .delta-line { padding:4px 6px; border-radius:8px; background:rgba(255,255,255,0.06); color:var(--text-color); position:relative; overflow:hidden; }
    .change-col .delta-line:before { content:""; position:absolute; inset:0; background:linear-gradient(135deg,rgba(255,255,255,0.08),rgba(255,255,255,0)); opacity:0.4; pointer-events:none; }
    .change-col .delta-line.pos { background:linear-gradient(135deg,#059669,#10b981); color:#fff; }
    .change-col .delta-line.neg { background:linear-gradient(135deg,#dc2626,#ef4444); color:#fff; }
    .change-col .delta-line.neutral { background:rgba(255,255,255,0.08); color:var(--text-dim); }
    .change-col .delta-line + .delta-line { margin-top:4px; }
    .change-col .delta-line .delta-metric { opacity:.9; }
    .change-col .delta-line.neg .delta-metric, .change-col .delta-line.pos .delta-metric { color:#fff; }
    /* Dark mode table refinement */
    /* Dark theme table unify with card design */
    body:not(.facts-mode):not(.light-theme) .data-table { 
        background: linear-gradient(155deg, var(--card-bg), rgba(255,255,255,0.02)); 
        box-shadow: var(--shadow-color); 
        border:1px solid var(--card-border);
    }
    body:not(.facts-mode):not(.light-theme) .data-table tbody tr:hover { background: rgba(255,255,255,0.05); }
    body:not(.facts-mode):not(.light-theme) .data-table th { color:#fff; }
    /* Row height consistency */
    .data-table td.change-col { min-width:150px; }
    .change-col .delta-line { white-space:nowrap; min-height:22px; }
    .w-cell .bar-wrap { height:5px; margin-top:2px; }
    .w-cell { gap:2px !important; }
    /* Center alignment for Változás column */
    .data-table td.change-col { text-align:center !important; }
    .change-col { align-items:center !important; }
    .change-col .delta-line { justify-content:center !important; }
    /* Column widths */
    .data-table th:nth-child(1), .data-table td:nth-child(1){ width:24%; }
    .data-table th:nth-child(2), .data-table td:nth-child(2){ width:18%; }
    .data-table th:nth-child(3), .data-table td:nth-child(3){ width:12%; }
    .data-table th:nth-child(4), .data-table td:nth-child(4){ width:12%; }
    .data-table th:nth-child(5), .data-table td:nth-child(5){ width:34%; }
    /* Sticky header optional base (not active when facts-mode label strict) */
    body:not(.facts-mode) .data-table thead th { position:sticky; top:0; z-index:5; }
    /* Dark label skin (automatic) */
    body.facts-mode:not(.light-theme) .data-table { background: linear-gradient(145deg, var(--card-bg-solid), rgba(255,255,255,0.04)); border:1px solid var(--card-border); color:var(--text-color); }
    body.facts-mode:not(.light-theme) .data-table thead { background: linear-gradient(90deg,var(--accent-start),var(--accent-end)); color:#fff; }
    body.facts-mode:not(.light-theme) .data-table th { color:#fff; }
    body.facts-mode:not(.light-theme) .data-table td .percentage { background:rgba(255,255,255,0.08); color:#fff; }
    body.facts-mode:not(.light-theme).facts-color tr[data-waste-band="high"] { box-shadow: inset 4px 0 0 #ef4444; }
    body.facts-mode:not(.light-theme).facts-color tr[data-waste-band="low"] { box-shadow: inset 4px 0 0 #10b981; }
    /* Enhanced trend controls */
    .enhanced-trend .chart-header-row { display:flex; gap:14px; flex-wrap:wrap; align-items:center; }
    .chart-controls { display:flex; gap:10px; align-items:center; flex-wrap:wrap; margin-left:auto; }
    .metric-toggle-group { display:flex; background:rgba(255,255,255,0.05); border:1px solid var(--card-border); border-radius:999px; padding:2px; }
    .metric-toggle-group .mt-btn { background:transparent; border:none; padding:5px 12px; font-size:.65rem; letter-spacing:.5px; font-weight:600; color:var(--text-color); border-radius:999px; cursor:pointer; transition:.2s background,.2s color; }
    .metric-toggle-group .mt-btn.active { background:linear-gradient(90deg,var(--accent-start),var(--accent-end)); color:#fff; }
    .ma-group { display:flex; gap:4px; align-items:center; background:rgba(255,255,255,0.06); border:1px solid var(--card-border); padding:4px 8px; border-radius:10px; }
    .ma-group label { font-size:.55rem; font-weight:600; letter-spacing:.5px; opacity:.7; }
    .ma-group select { background:transparent; border:none; color:var(--text-color); font-size:.65rem; font-weight:600; cursor:pointer; }
    .ma-group select option { color:#000; }
    .pin-size-switch { display:flex; gap:4px; background:rgba(255,255,255,0.06); border:1px solid var(--card-border); padding:4px 6px; border-radius:10px; }
    .pin-size-switch button { background:transparent; border:none; color:var(--text-color); cursor:pointer; font-size:.6rem; font-weight:600; width:24px; height:24px; border-radius:6px; display:flex; align-items:center; justify-content:center; }
    .pin-size-switch button.active, .pin-size-switch button:hover { background:linear-gradient(90deg,var(--accent-start),var(--accent-end)); color:#fff; }
    .clear-pins-btn { background:rgba(255,255,255,0.06); border:1px solid var(--card-border); color:var(--text-color); width:32px; height:32px; border-radius:10px; cursor:pointer; display:flex; align-items:center; justify-content:center; font-size:.85rem; }
    .clear-pins-btn:hover { background:rgba(255,255,255,0.12); }
    .clear-pins-btn:active { transform:scale(.92); }
    /* Palette selector */
    .palette-host { margin:4px 0 8px; }
    .palette-grid { display:grid; grid-template-columns:repeat(auto-fill,minmax(120px,1fr)); gap:6px; }
    .palette-grid button { position:relative; border:1px solid var(--card-border); background:rgba(255,255,255,0.04); padding:4px 4px 18px; border-radius:10px; cursor:pointer; display:flex; gap:4px; flex-wrap:wrap; justify-content:flex-start; min-height:56px; transition:.2s border, .2s background; }
    .palette-grid button:hover { background:rgba(255,255,255,0.08); }
    .palette-grid button.active { outline:2px solid var(--accent-end); outline-offset:2px; }
    .palette-grid .sw { width:18px; height:18px; border-radius:6px; flex:0 0 auto; box-shadow:0 0 0 1px rgba(255,255,255,0.15) inset; }
    .palette-grid .pal-name { position:absolute; left:6px; bottom:4px; font-size:.55rem; letter-spacing:.5px; text-transform:uppercase; opacity:.75; font-weight:600; pointer-events:none; }
    @media (max-width:900px){ .palette-grid { grid-template-columns:repeat(auto-fill,minmax(90px,1fr)); } .palette-grid .sw { width:14px; height:14px; } }
    </style>
</head>
<body>
    <button class="controls-panel-toggle" id="openCustomizer" title="Testreszabás / Color Customizer">🎨 Színek</button>
    <div class="controls-panel" id="customizerPanel" aria-label="Szín testreszabás panel">
        <h3>Színek</h3>
        <div class="color-row"><label for="accentStart">Fő Szín 1</label><input type="color" id="accentStart"></div>
        <div class="color-row"><label for="accentEnd">Fő Szín 2</label><input type="color" id="accentEnd"></div>
        <div class="color-row"><label for="accentAlt">Alternatív</label><input type="color" id="accentAlt"></div>
        <div class="color-row"><label for="bgStart">Háttér 1</label><input type="color" id="bgStart"></div>
        <div class="color-row"><label for="bgEnd">Háttér 2</label><input type="color" id="bgEnd"></div>
        <div class="color-row"><label for="cardBg">Kártya BG</label><input type="color" id="cardBg"></div>
        <div class="panel-divider"></div>
    <h4 style="margin:6px 0 4px;font-size:.8rem;letter-spacing:.5px;text-transform:uppercase;opacity:.75;">Paletták</h4>
    <div id="paletteContainer" class="palette-host" aria-label="Előre definiált szín paletták"></div>
    <div class="panel-divider"></div>
    <button class="theme-toggle" id="toggleTheme">Váltás Világos / Sötét</button>
    <button class="facts-toggle-btn" id="toggleFacts">Táblázat Stílus: Modern ➜ Label</button>
    <button class="facts-color-btn" id="toggleFactsColor">Label Színezés: KI</button>
    <button class="reset-btn" id="resetColors">Alapértelmezett</button>
        <div class="tiny">Beállítások mentve a böngészőben</div>
    </div>
    <div class="container">
        <div class="header">
            <h1>📊 Pénzügyi Dashboard</h1>
            <p>Havi teljesítmény adatok és trendek 2022-2025</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="totalWaste">-</div>
                <div class="stat-label">Összes Selejt (HUF)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgMargin">-</div>
                <div class="stat-label">Átlag Árrés (%)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgWastePercent">-</div>
                <div class="stat-label">Átlag Selejt (%)</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="monthCount">32</div>
                <div class="stat-label">Hónapok</div>
            </div>
        </div>

        <div class="layout-row">
            <div class="card table-container table-card">
                <h2><span class="icon">📋</span>Részletes Adatok</h2>
                <!-- Szűrő panel áthelyezve külön lebegő panelbe -->
                <div class="data-table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Dátum</th>
                                <th class="number">Selejt HUF</th>
                                <th class="number">Árrés %</th>
                                <th class="number">Selejt %</th>
                                <th class="number">Változás</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody"></tbody>
                    </table>
                </div>
            </div>
            <div class="mini-column">
                <div class="legend-card" id="legendCard">
                    <h4>Jelmagyarázat</h4>
                    <div class="legend-grid">
                        <div class="legend-item"><span class="legend-swatch wastebar"></span><span>Selejt HUF arány (max=100%)</span></div>
                        <div class="legend-item"><span class="legend-swatch margin line"></span><span>Árrés % trend</span></div>
                        <div class="legend-item"><span class="legend-swatch wastepct line"></span><span>Selejt % trend</span></div>
                        <div class="legend-item"><span class="legend-swatch pos"></span><span>Pozitív: Árrés nő / Selejt% csökken</span></div>
                        <div class="legend-item"><span class="legend-swatch neg"></span><span>Negatív: Árrés csökken / Selejt% nő</span></div>
                        <div class="legend-item"><span class="legend-swatch" style="background:#dc2626"></span><span>Magas selejt% sáv</span></div>
                        <div class="legend-item"><span class="legend-swatch" style="background:#059669"></span><span>Alacsony selejt% sáv</span></div>
                        <div class="legend-item legend-mini" style="grid-column:1/-1">Index bázis: első év átlag = 100</div>
                    </div>
                </div>
                <div class="mini-card" id="miniWasteCard">
                    <h4>Selejt HUF Mini</h4>
                    <div class="value-badge" id="miniWasteValue">-</div>
                    <canvas id="miniWasteChart"></canvas>
                </div>
                <div class="mini-card" id="miniMarginCard">
                    <h4>Árrés % Mini</h4>
                    <div class="value-badge" id="miniMarginValue">-</div>
                    <canvas id="miniMarginChart"></canvas>
                </div>
                <div class="mini-card" id="miniWastePctCard">
                    <h4>Selejt % Mini</h4>
                    <div class="value-badge" id="miniWastePctValue">-</div>
                    <canvas id="miniWastePctChart"></canvas>
                </div>
            </div>
        </div>
        <div class="card trend-wide enhanced-trend">
            <div class="chart-header-row">
                <h2><span class="icon">📈</span>Trend</h2>
                <div class="chart-controls" id="trendControls" aria-label="Mutató beállítások">
                    <div class="metric-toggle-group" role="group" aria-label="Mutatók">
                        <button type="button" class="mt-btn active" data-metric="waste">Selejt HUF</button>
                        <button type="button" class="mt-btn" data-metric="margin">Árrés %</button>
                        <button type="button" class="mt-btn" data-metric="wastePct">Selejt %</button>
                    </div>
                    <div class="ma-group" aria-label="Mozgóátlag">
                        <label for="maSelect">MA</label>
                        <select id="maSelect">
                            <option value="0">Ki</option>
                            <option value="3">3</option>
                            <option value="6">6</option>
                            <option value="12">12</option>
                        </select>
                    </div>
                    <div class="pin-size-switch" id="pinSizeSwitch" aria-label="Tooltip méret">
                        <button data-size="sm" aria-label="Kicsi">S</button>
                        <button data-size="md" class="active" aria-label="Közepes">M</button>
                        <button data-size="lg" aria-label="Nagy">L</button>
                    </div>
                    <button type="button" id="clearPins" class="clear-pins-btn" title="Összes rögzített tooltip törlése">🧹</button>
                </div>
            </div>
            <div class="chart-container" style="height:480px; position:relative;">
                <canvas id="trendChart"></canvas>
            </div>
        </div>
        <div class="two-col-row">
            <div class="card yearly-card">
                <h2><span class="icon">🎯</span>Éves Összehasonlítás</h2>
                <div class="chart-container" style="height:420px">
                    <canvas id="yearlyChart"></canvas>
                </div>
                <div class="year-info-panel" id="yearInfoPanel">
                    <div class="year-info-header">
                        <span id="infoYearLabel">Válassz évet / hover</span>
                        <div class="mode-switch">
                            <button class="mode-btn active" data-mode="raw" id="modeRaw">Érték</button>
                            <button class="mode-btn" data-mode="index" id="modeIndex">Index</button>
                        </div>
                    </div>
                    <div class="year-info-grid">
                        <div class="info-box">
                            <div class="info-label">Átl. Selejt HUF</div>
                            <div class="info-value" id="infoAvgWaste">-</div>
                            <div class="delta" id="deltaWaste"></div>
                        </div>
                        <div class="info-box">
                            <div class="info-label">Átl. Árrés %</div>
                            <div class="info-value" id="infoAvgMargin">-</div>
                            <div class="delta" id="deltaMargin"></div>
                        </div>
                        <div class="info-box">
                            <div class="info-label">Átl. Selejt %</div>
                            <div class="info-value" id="infoAvgWastePct">-</div>
                            <div class="delta" id="deltaWastePct"></div>
                        </div>
                    </div>
                    <div class="info-footer">
                        <span id="footerSample">Forrás: havi átlagok összesítése.</span>
                        <span id="footerModeNote">Mód: Érték</span>
                    </div>
                </div>
            </div>
            <div class="card placeholder-card" id="placeholderCard">
                <h2><span class="icon">🧩</span>Helyfoglaló</h2>
                <div style="display:flex; flex-direction:column; gap:8px;">
                   <p style="font-size:.75rem; line-height:1.35; color:var(--text-dim); margin:2px 0 4px;">Éves összefoglaló kártyák – három nézet: kombinált Érték + Index, részletes bontás és mátrix. Válassz alul.</p>
                   <div class="variant-switch" id="variantSwitch">
                       <button class="variant-btn active" data-variant="1">V1 Lista</button>
                       <button class="variant-btn" data-variant="2">V2 Kártyák</button>
                       <button class="variant-btn" data-variant="3">V3 Mátrix</button>
                   </div>
                   <div id="variantContent" class="variant-content"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Original data from Excel
        const rawData = [
            ["2022-12-31T23:00:00.000Z", 429566, 0.321, 0.028],
            ["2023-01-31T23:00:00.000Z", 211051, 0.32, 0.014],
            ["2023-02-28T23:00:00.000Z", 300051, 0.273, 0.017],
            ["2023-03-31T22:00:00.000Z", 349641, 0.29, 0.024],
            ["2023-04-30T22:00:00.000Z", 343470, 0.298, 0.02],
            ["2023-05-31T22:00:00.000Z", 421373, 0.288, 0.028],
            ["2023-06-30T22:00:00.000Z", 390375, 0.276, 0.03],
            ["2023-07-31T22:00:00.000Z", 375140, 0.24, 0.026],
            ["2023-08-31T22:00:00.000Z", 284146, 0.298, 0.017],
            ["2023-09-30T22:00:00.000Z", 278952, 0.259, 0.017],
            ["2023-10-31T23:00:00.000Z", 132999, 0.35, 0.008],
            ["2023-11-30T23:00:00.000Z", 196828, 0.318, 0.014],
            ["2023-12-31T23:00:00.000Z", 150022, 0.272, 0.01],
            ["2024-01-31T23:00:00.000Z", 171387, 0.322, 0.01],
            ["2024-02-29T23:00:00.000Z", 203969, 0.316, 0.012],
            ["2024-03-31T22:00:00.000Z", 156747, 0.205, 0.01],
            ["2024-04-30T22:00:00.000Z", 196673, 0.191, 0.011],
            ["2024-05-31T22:00:00.000Z", 168188, 0.28, 0.012],
            ["2024-06-30T22:00:00.000Z", 139796, 0.246, 0.01],
            ["2024-07-31T22:00:00.000Z", 147551, 0.234, 0.011],
            ["2024-08-31T22:00:00.000Z", 183731, 0.292, 0.011],
            ["2024-09-30T22:00:00.000Z", 203242, 0.254, 0.011],
            ["2024-10-31T23:00:00.000Z", 309666, 0.299, 0.019],
            ["2024-11-30T23:00:00.000Z", 414283, 0.277, 0.029],
            ["2024-12-31T23:00:00.000Z", 296550, 0.279, 0.019],
            ["2025-01-31T23:00:00.000Z", 352828, 0.308, 0.022],
            ["2025-02-28T23:00:00.000Z", 446357, 0.303, 0.025],
            ["2025-03-31T22:00:00.000Z", 584288, 0.313, 0.033],
            ["2025-04-30T22:00:00.000Z", 471640, 0.263, 0.024],
            ["2025-05-31T22:00:00.000Z", 542692, 0.307, 0.037],
            ["2025-06-30T22:00:00.000Z", 518160, 0.307, 0.034],
            ["2025-07-31T22:00:00.000Z", 384280, 0.278, 0.027]
        ];

        // Process data (full immutable base set)
        const processedData = rawData.map(row => ({
            date: new Date(row[0]),
            dateString: new Date(row[0]).toLocaleDateString('hu-HU', { year: 'numeric', month: 'long' }),
            wasteHUF: row[1],
            marginPercent: row[2] * 100,
            wastePercent: row[3] * 100
        }));

    // Global filtering state
    const DATE_FILTER_KEY = 'dashDateFilterV1';
    let fullData = [...processedData];
    let activeData = [...processedData];
    let currentDateFilter = null; // {from: Date|null, to: Date|null, preset: string|null}

    let trendChart, yearlyChart;
    let miniWasteChart, miniMarginChart, miniWastePctChart;
        let originalData = [...processedData];

        // === THEME & COLOR CUSTOMIZATION ======================================
        const root = document.documentElement;
        const STORAGE_KEY = 'dashThemeColorsV1';
    const THEME_KEY = 'dashThemeModeV1';
    const FACTS_KEY = 'dashFactsModeV1';
    const FACTS_COLOR_KEY = 'dashFactsColorV1';
    const PALETTE_KEY = 'dashPaletteV1';

        const defaultColors = {
            '--accent-start': getComputedStyle(root).getPropertyValue('--accent-start').trim(),
            '--accent-end': getComputedStyle(root).getPropertyValue('--accent-end').trim(),
            '--accent-alt': getComputedStyle(root).getPropertyValue('--accent-alt').trim(),
            '--bg-gradient-start': getComputedStyle(root).getPropertyValue('--bg-gradient-start').trim(),
            '--bg-gradient-end': getComputedStyle(root).getPropertyValue('--bg-gradient-end').trim(),
            '--card-bg': getComputedStyle(root).getPropertyValue('--card-bg').trim()
        };

        // === PREDEFINED COLOR PALETTES =======================================
        const COLOR_PALETTES = [
            { id:'aurora', name:'Aurora', vars:{
                '--accent-start':'#0D3B66','--accent-end':'#F4D35E','--accent-alt':'#EE964B','--accent-neutral':'#FAF0CA','--accent-warn':'#F95738','--bg-gradient-start':'#0D3B66','--bg-gradient-end':'#0D3B66','--card-bg':'rgba(13,59,102,0.30)'
            }},
            { id:'modern', name:'Modern Tech', vars:{
                '--accent-start':'#1B1F3B','--accent-end':'#2E86AB','--accent-alt':'#A23B72','--accent-neutral':'#EDE7E3','--accent-warn':'#F18F01','--bg-gradient-start':'#1B1F3B','--bg-gradient-end':'#2E86AB','--card-bg':'rgba(27,31,59,0.35)'
            }},
            { id:'mint', name:'Fresh Mint', vars:{
                '--accent-start':'#2D6A4F','--accent-end':'#40916C','--accent-alt':'#95D5B2','--accent-neutral':'#D8F3DC','--accent-warn':'#FFB703','--bg-gradient-start':'#2D6A4F','--bg-gradient-end':'#40916C','--card-bg':'rgba(45,106,79,0.30)'
            }},
            { id:'sunset', name:'Sunset Calm', vars:{
                '--accent-start':'#22223B','--accent-end':'#4A4E69','--accent-alt':'#9A8C98','--accent-neutral':'#C9ADA7','--accent-warn':'#F28482','--bg-gradient-start':'#22223B','--bg-gradient-end':'#4A4E69','--card-bg':'rgba(34,34,59,0.32)'
            }},
            { id:'neon', name:'Neon Contrast', vars:{
                '--accent-start':'#0A0221','--accent-end':'#63ADF2','--accent-alt':'#FE5F55','--accent-neutral':'#FFC145','--accent-warn':'#3E8989','--bg-gradient-start':'#0A0221','--bg-gradient-end':'#0A0221','--card-bg':'rgba(10,2,33,0.36)'
            }},
            { id:'carbon', name:'Carbon Citrus', vars:{
                '--accent-start':'#0F0F0F','--accent-end':'#FF6B35','--accent-alt':'#4ECDC4','--accent-neutral':'#F7C59F','--accent-warn':'#FFB400','--bg-gradient-start':'#0F0F0F','--bg-gradient-end':'#1F2933','--card-bg':'rgba(15,15,15,0.40)'
            }},
            { id:'pastel', name:'Data Pastel', vars:{
                '--accent-start':'#264653','--accent-end':'#2A9D8F','--accent-alt':'#E9C46A','--accent-neutral':'#F4A261','--accent-warn':'#E76F51','--bg-gradient-start':'#264653','--bg-gradient-end':'#2A9D8F','--card-bg':'rgba(38,70,83,0.33)'
            }}
        ];

        function applyPalette(id){
            const p = COLOR_PALETTES.find(x=>x.id===id); if(!p) return;
            Object.entries(p.vars).forEach(([k,v])=> root.style.setProperty(k, v));
            // persist main theme vars using existing mechanism
            saveColors();
            localStorage.setItem(PALETTE_KEY, id);
            updateChartColors();
            // refresh inputs so they match palette
            populateColorInputs();
            // mark active button
            const grid = document.getElementById('paletteGrid');
            if(grid){
                grid.querySelectorAll('button[data-pal]').forEach(b=> b.classList.toggle('active', b.dataset.pal===id));
            }
        }

        function applyStoredTheme() {
            try {
                const stored = JSON.parse(localStorage.getItem(STORAGE_KEY) || 'null');
                if (stored) {
                    Object.entries(stored).forEach(([k,v]) => root.style.setProperty(k, v));
                }
                const mode = localStorage.getItem(THEME_KEY);
                if (mode === 'light') document.body.classList.add('light-theme');
                const facts = localStorage.getItem(FACTS_KEY);
                if (facts === 'on') document.body.classList.add('facts-mode');
                const factsColor = localStorage.getItem(FACTS_COLOR_KEY);
                if (factsColor === 'on') document.body.classList.add('facts-color');
            } catch (e) { /* ignore */ }
        }

        function saveColors() {
            const current = {};
            Object.keys(defaultColors).forEach(k => current[k] = getComputedStyle(root).getPropertyValue(k).trim());
            localStorage.setItem(STORAGE_KEY, JSON.stringify(current));
        }

        function updateColorVar(varName, value) {
            root.style.setProperty(varName, value);
            saveColors();
            updateChartColors();
        }

        function resetColors() {
            Object.entries(defaultColors).forEach(([k,v]) => root.style.setProperty(k, v));
            saveColors();
            populateColorInputs();
            updateChartColors();
        }

        function toggleTheme() {
            document.body.classList.toggle('light-theme');
            const mode = document.body.classList.contains('light-theme') ? 'light' : 'dark';
            localStorage.setItem(THEME_KEY, mode);
            updateChartColors();
        }

        function populateColorInputs() {
            const byId = id => document.getElementById(id);
            byId('accentStart').value = rgbToHex(getComputedStyle(root).getPropertyValue('--accent-start'));
            byId('accentEnd').value = rgbToHex(getComputedStyle(root).getPropertyValue('--accent-end'));
            byId('accentAlt').value = rgbToHex(getComputedStyle(root).getPropertyValue('--accent-alt'));
            byId('bgStart').value = rgbToHex(getComputedStyle(root).getPropertyValue('--bg-gradient-start'));
            byId('bgEnd').value = rgbToHex(getComputedStyle(root).getPropertyValue('--bg-gradient-end'));
            // Convert rgba to hex approximated
            byId('cardBg').value = rgbToHex(getComputedStyle(root).getPropertyValue('--card-bg'));
        }

        function rgbToHex(rgb) {
            if(!rgb) return '#000000';
            // rgb or rgba string -> hex
            const m = rgb.match(/rgba?\((\d+)?,?\s*(\d+)?,?\s*(\d+)/i);
            if (!m) return '#000000';
            return '#' + [m[1], m[2], m[3]].map(x => (parseInt(x).toString(16).padStart(2,'0'))).join('');
        }

        function hexToRgba(hex, alpha = 0.85) {
            const h = hex.replace('#','');
            const bigint = parseInt(h, 16);
            const r = (bigint >> 16) & 255;
            const g = (bigint >> 8) & 255;
            const b = bigint & 255;
            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
        }

        function initCustomizer() {
            const panel = document.getElementById('customizerPanel');
            const toggleBtn = document.getElementById('openCustomizer');
            const resetBtn = document.getElementById('resetColors');
            const themeToggle = document.getElementById('toggleTheme');
            const factsToggle = document.getElementById('toggleFacts');
            const factsColorToggle = document.getElementById('toggleFactsColor');
            const storedPalette = localStorage.getItem(PALETTE_KEY);
            if(storedPalette){
                // apply before inputs populate
                applyPalette(storedPalette);
            }

            toggleBtn.addEventListener('click', () => {
                panel.classList.toggle('open');
            });

            resetBtn.addEventListener('click', resetColors);
            themeToggle.addEventListener('click', toggleTheme);

            const mapping = {
                accentStart: '--accent-start',
                accentEnd: '--accent-end',
                accentAlt: '--accent-alt',
                bgStart: '--bg-gradient-start',
                bgEnd: '--bg-gradient-end',
                cardBg: '--card-bg'
            };

            factsToggle.addEventListener('click', () => {
                document.body.classList.toggle('facts-mode');
                const on = document.body.classList.contains('facts-mode');
                localStorage.setItem(FACTS_KEY, on ? 'on' : 'off');
                factsToggle.textContent = on ? 'Táblázat Stílus: Label ➜ Modern' : 'Táblázat Stílus: Modern ➜ Label';
            });

            factsColorToggle.addEventListener('click', () => {
                document.body.classList.toggle('facts-color');
                const on = document.body.classList.contains('facts-color');
                localStorage.setItem(FACTS_COLOR_KEY, on ? 'on' : 'off');
                factsColorToggle.textContent = 'Label Színezés: ' + (on ? 'BE' : 'KI');
            });

            // Initialize button label based on state
            factsToggle.textContent = document.body.classList.contains('facts-mode') ? 'Táblázat Stílus: Label ➜ Modern' : 'Táblázat Stílus: Modern ➜ Label';
            const fcOn = document.body.classList.contains('facts-color');
            factsColorToggle.textContent = 'Label Színezés: ' + (fcOn ? 'BE' : 'KI');

            Object.entries(mapping).forEach(([id,varName]) => {
                const el = document.getElementById(id);
                el.addEventListener('input', (e) => {
                    if (varName === '--card-bg') {
                        updateColorVar(varName, hexToRgba(e.target.value, 0.85));
                    } else {
                        updateColorVar(varName, e.target.value);
                    }
                    // user manually changed -> detach palette selection
                    localStorage.removeItem(PALETTE_KEY);
                    const grid = document.getElementById('paletteGrid');
                    if(grid){ grid.querySelectorAll('button[data-pal]').forEach(b=> b.classList.remove('active')); }
                });
            });

            // Build palette selector UI
            const paletteHost = document.getElementById('paletteContainer');
            if(paletteHost){
                const html = `
                    <div class="palette-grid" id="paletteGrid">
                        ${COLOR_PALETTES.map(p=>{
                            const sw = ['--accent-start','--accent-end','--accent-alt','--accent-neutral','--accent-warn'].map(v=>`<span class="sw" style="background:${p.vars[v]||'#000'}"></span>`).join('');
                            return `<button type="button" data-pal="${p.id}" title="${p.name}" aria-label="${p.name}">${sw}<span class="pal-name">${p.name}</span></button>`;
                        }).join('')}
                    </div>`;
                paletteHost.innerHTML = html;
                paletteHost.addEventListener('click', e=>{
                    const btn = e.target.closest('button[data-pal]'); if(!btn) return;
                    applyPalette(btn.dataset.pal);
                });
                if(storedPalette){
                    const activeBtn = paletteHost.querySelector(`button[data-pal="${storedPalette}"]`);
                    if(activeBtn) activeBtn.classList.add('active');
                }
            }

            populateColorInputs();
        }

        function updateChartColors() {
            if (!trendChart || !yearlyChart) return;
            const accentStart = getComputedStyle(root).getPropertyValue('--accent-start').trim();
            const accentEnd = getComputedStyle(root).getPropertyValue('--accent-end').trim();
            const accentAlt = getComputedStyle(root).getPropertyValue('--accent-alt').trim();

            // Gradient for line fill
            const ctx = trendChart.ctx;
            const grad = ctx.createLinearGradient(0,0,0,300);
            grad.addColorStop(0, accentStart + 'EE');
            grad.addColorStop(1, accentEnd + '22');

            trendChart.data.datasets[0].borderColor = accentStart;
            trendChart.data.datasets[0].pointBackgroundColor = accentStart;
            trendChart.data.datasets[0].backgroundColor = grad;
            trendChart.update('none');

            yearlyChart.data.datasets[0].backgroundColor = [accentStart, accentEnd, accentAlt];
            yearlyChart.update('none');

            if (miniWasteChart && miniMarginChart && miniWastePctChart) {
                const palette = [accentStart, accentEnd, accentAlt];
                [miniWasteChart, miniMarginChart, miniWastePctChart].forEach((c,i) => {
                    c.data.datasets[0].borderColor = palette[i] || accentStart;
                    c.data.datasets[0].backgroundColor = (palette[i] || accentStart) + '33';
                    c.update('none');
                });
            }
        }

        // Mini charts
        function initMiniCharts() {
            if (miniWasteChart) return; // prevent double init
            const labels = activeData.map(d => d.dateString.split(' ')[0].slice(0,3));
            const minimalTicks = (vals) => {
                if(!vals.length) return {min:0,max:1};
                let min = Math.min(...vals), max = Math.max(...vals);
                if(min===max){ max = min + 1; }
                return {min, max};
            };
            const mkLineOpts = (vals) => {
                const r = minimalTicks(vals);
                return {
                    type: 'line',
                    data: { labels, datasets: [{ data: vals, borderWidth: 2, pointRadius: 0, tension: .35, fill: true }] },
                    options: { responsive: true, maintainAspectRatio: false, plugins:{legend:{display:false}},
                        scales:{
                            x:{display:false},
                            y:{display:true, grid:{display:false}, ticks:{
                                maxTicksLimit:3,
                                callback:(v)=>''+ (Math.round(v)>=1000? (Math.round(v/1000)+'k') : Math.round(v))
                            }, suggestedMin:r.min, suggestedMax:r.max }
                        },
                        layout:{padding:{top:2,bottom:0,left:0,right:0}}
                    }
                };
            };
            // Waste mini will be column/bar
            const wasteVals = activeData.map(d=>d.wasteHUF);
            const marginVals = activeData.map(d=>d.marginPercent);
            const wastePctVals = activeData.map(d=>d.wastePercent);
            const wasteRange = minimalTicks(wasteVals);
            const ctxWaste = document.getElementById('miniWasteChart')?.getContext('2d');
            const ctxMargin = document.getElementById('miniMarginChart')?.getContext('2d');
            const ctxWastePct = document.getElementById('miniWastePctChart')?.getContext('2d');
            if(!ctxWaste || !ctxMargin || !ctxWastePct) return;
            miniWasteChart = new Chart(ctxWaste, {
                type:'bar',
                data:{ labels, datasets:[{ data:wasteVals, backgroundColor:getComputedStyle(root).getPropertyValue('--accent-start').trim()+'55', borderWidth:0, borderRadius:3 }]},
                options:{ responsive:true, maintainAspectRatio:false, plugins:{legend:{display:false}}, scales:{ x:{display:false}, y:{display:true, grid:{display:false}, ticks:{maxTicksLimit:3, callback:(v)=>''+(Math.round(v)>=1000? (Math.round(v/1000)+'k'):Math.round(v))}, suggestedMin:wasteRange.min, suggestedMax:wasteRange.max } }, layout:{padding:{top:0,bottom:0,left:0,right:0}} }
            });
            miniMarginChart = new Chart(ctxMargin, mkLineOpts(marginVals));
            miniWastePctChart = new Chart(ctxWastePct, mkLineOpts(wastePctVals));
        }

        function updateMiniBadges() {
            const last = activeData[activeData.length-1];
            if(!last) return;
            const wasteEl = document.getElementById('miniWasteValue');
            const marginEl = document.getElementById('miniMarginValue');
            const wastePctEl = document.getElementById('miniWastePctValue');
            if(wasteEl) wasteEl.textContent = formatHUF(last.wasteHUF);
            if(marginEl) marginEl.textContent = last.marginPercent.toFixed(1) + '%';
            if(wastePctEl) wastePctEl.textContent = last.wastePercent.toFixed(2) + '%';
        }

        // =====================================================================

        // Initialize dashboard
        function initDashboard() {
            applyStoredTheme();
            loadStoredDateFilter();
            applyActiveFilter(false);
            calculateStats();
            renderTable(activeData);
            initCharts();
            setupSearch();
            initCustomizer();
            initMiniCharts();
            updateChartColors();
            updateMiniBadges();
            initDateFilterUI();
            initDataPanel();
            initTrendControls();
            // initial metric ensure proper scales and MA hidden
            setTrendMetric('waste');
        }

        function calculateStats() {
            const data = activeData;
            const totalWaste = data.reduce((sum, row) => sum + row.wasteHUF, 0);
            const avgMargin = data.reduce((sum, row) => sum + row.marginPercent, 0) / (data.length||1);
            const avgWastePercent = data.reduce((sum, row) => sum + row.wastePercent, 0) / (data.length||1);

            document.getElementById('totalWaste').textContent = formatHUF(totalWaste);
            document.getElementById('avgMargin').textContent = avgMargin.toFixed(1) + '%';
            document.getElementById('avgWastePercent').textContent = avgWastePercent.toFixed(2) + '%';
        }

        function formatHUF(amount) {
            return new Intl.NumberFormat('hu-HU', {
                style: 'currency',
                currency: 'HUF',
                minimumFractionDigits: 0
            }).format(amount);
        }

        function renderTable(data) {
            const tbody = document.getElementById('dataTableBody');
            tbody.innerHTML = '';
            if(!data.length) return;
            // Precompute for bars & deltas
            const maxWaste = Math.max(...data.map(d=>d.wasteHUF));
            const HIGH_WASTE_PCT = 3.0; // >3% high
            const LOW_WASTE_PCT = 2.0;  // <2% low
            const LOW_MARGIN_PCT = 24.0; // alert if below
            let prev = null;
                data.forEach(row => {
                     const tr = document.createElement('tr');
                     // Waste band classification
                     let band = 'mid';
                     if(row.wastePercent > HIGH_WASTE_PCT) band = 'high';
                     else if(row.wastePercent < LOW_WASTE_PCT) band = 'low';
                     tr.dataset.wasteBand = band;
                     if(row.marginPercent < LOW_MARGIN_PCT) tr.dataset.alert = 'low-margin';
                     if(row.wastePercent > HIGH_WASTE_PCT) tr.dataset.alert = 'high-waste';
                     let marginDelta = null, wastePctDelta = null;
                     if(prev){
                          marginDelta = row.marginPercent - prev.marginPercent;
                          wastePctDelta = row.wastePercent - prev.wastePercent; // lower better
                     }
                     const fmtDelta = v => v==null? '—' : (v>0?'+':'') + Math.abs(v).toFixed(2)+'%';
                     const wasteBar = ((row.wasteHUF / (maxWaste||1))*100).toFixed(1)+'%';
                    const marginArrow = marginDelta==null? '' : (marginDelta>0?'▲':'▼');
                    const wasteArrow = wastePctDelta==null? '' : (wastePctDelta<0?'▲':'▼');
                    const marginLineClass = marginDelta==null? 'neutral' : (marginDelta>0? 'pos':'neg');
                    const wasteLineClass = wastePctDelta==null? 'neutral' : (wastePctDelta<0? 'pos':'neg');
                     const ariaCombined = `Árrés változás ${fmtDelta(marginDelta).replace('+','plusz')} ; Selejt százalék változás ${fmtDelta(wastePctDelta).replace('+','plusz')}`;
                     tr.innerHTML = `
                          <td>${row.dateString}</td>
                          <td class="number w-cell">
                              <span class="w-amt">${formatHUF(row.wasteHUF)}</span>
                              <span class="bar-wrap"><span class="bar-fill" style="--w:${wasteBar}"></span></span>
                          </td>
                          <td class="number"><span class="percentage">${row.marginPercent.toFixed(1)}%</span></td>
                          <td class="number"><span class="percentage">${row.wastePercent.toFixed(2)}%</span></td>
                              <td class="number change-col" aria-label="${ariaCombined}">
                                  <span class="delta-line ${marginLineClass}">
                                      <span class="delta-metric">ÁRRÉS</span>
                                      <span class="delta-arrow">${marginArrow}</span>
                                      <span class="delta-val">${fmtDelta(marginDelta)}</span>
                                  </span>
                                  <span class="delta-line ${wasteLineClass}">
                                      <span class="delta-metric">SELEJT%</span>
                                      <span class="delta-arrow">${wasteArrow}</span>
                                      <span class="delta-val">${fmtDelta(wastePctDelta)}</span>
                                  </span>
                              </td>`;
                     tbody.appendChild(tr);
                     prev = row;
                });
        }

        function initCharts() {
            // Trend Chart
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            const pinnedTooltips = []; // {id, x,y, html}
            const tooltipContainer = (canvas)=>{
                let c = canvas.parentNode.querySelector('.pinned-tooltips');
                if(!c){
                    c = document.createElement('div');
                    c.className='pinned-tooltips';
                    Object.assign(c.style,{position:'absolute',left:0,top:0,pointerEvents:'none'});
                    canvas.parentNode.style.position='relative';
                    canvas.parentNode.appendChild(c);
                }
                return c;
            };
            function addPinned(canvas, chart, point){
                const meta = point.element || point;
                const rect = canvas.getBoundingClientRect();
                const box = chart.canvas.getBoundingClientRect();
                const id = Date.now()+Math.random();
                const value = point.element?.$context?.raw ?? point.raw;
                const label = chart.data.labels[point.index];
                const div = document.createElement('div');
                div.className='pinned-tip';
                Object.assign(div.style,{
                    position:'absolute',
                    left:(meta.x)+'px', top:(meta.y)+'px',
                    transform:'translate(-50%,-110%)',
                    background:'rgba(15,23,42,0.92)',
                    backdropFilter:'blur(6px)',
                    border:'1px solid var(--card-border)',
                    borderRadius:'10px',
                    padding:'6px 8px 8px',
                    fontSize:'.65rem',
                    lineHeight:'1.25',
                    color:'var(--text-color)',
                    boxShadow:'0 6px 18px -6px rgba(0,0,0,0.6)',
                    pointerEvents:'auto'
                });
                div.innerHTML = `<button type="button" aria-label="Bezárás" style="position:absolute;top:2px;right:2px;background:transparent;border:none;color:var(--text-dim);cursor:pointer;font-size:.7rem;">×</button><div style="font-weight:600;white-space:nowrap;">${label}</div><div style="margin-top:2px;">${new Intl.NumberFormat('hu-HU').format(value)} Ft</div>`;
                div.querySelector('button').addEventListener('click',()=>{ div.remove(); });
                tooltipContainer(canvas).appendChild(div);
            }
            trendChart = new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: activeData.map(d => d.dateString),
                    datasets: [{
                        label: 'Selejt HUF',
                        data: activeData.map(d => d.wasteHUF),
                        borderColor: getComputedStyle(root).getPropertyValue('--accent-start').trim(),
                        backgroundColor: 'rgba(99, 102, 241, 0.15)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: getComputedStyle(root).getPropertyValue('--accent-start').trim(),
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('hu-HU').format(value);
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45
                            }
                        }
                    },
                    elements: {
                        point: {
                            hoverRadius: 8
                        }
                    }
                },
                plugins:[{
                    id:'pinClick',
                    afterEvent(chart, args){
                        const e = args.event;
                        if(e.type==='click'){
                            const points = chart.getElementsAtEventForMode(e,'nearest',{intersect:true}, true);
                            if(points.length){ addPinned(chart.canvas, chart, points[0]); }
                        }
                    }
                }]
            });

            // Yearly Comparison Chart
            window.yearlyData = calculateYearlyAverages();
            const yearlyData = window.yearlyData;
            const yearlyCtx = document.getElementById('yearlyChart').getContext('2d');
            // Custom plugin for always-on labels
            const barValuePlugin = {
                id: 'barValuePlugin',
                afterDatasetsDraw(chart, args, opts) {
                    if (chart.canvas.id !== 'yearlyChart') return;
                    const {ctx} = chart;
                    ctx.save();
                    const textColor = document.body.classList.contains('facts-mode') ? '#000' : getComputedStyle(root).getPropertyValue('--text-color').trim() || '#e2e8f0';
                    const dataset = chart.data.datasets[0];
                    chart.getDatasetMeta(0).data.forEach((bar, i) => {
                        const value = dataset.data[i];
                        if (value == null) return;
                        const formatted = new Intl.NumberFormat('hu-HU').format(Math.round(value)) + ' Ft';
                        const y = bar.y - 8; // above bar
                        ctx.font = '600 12px \'Segoe UI\', sans-serif';
                        ctx.fillStyle = textColor;
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'bottom';
                        ctx.fillText(formatted, bar.x, y);
                    });
                    ctx.restore();
                }
            };

            const accentStart = getComputedStyle(root).getPropertyValue('--accent-start').trim();
            const accentEnd = getComputedStyle(root).getPropertyValue('--accent-end').trim();
            const accentAlt = getComputedStyle(root).getPropertyValue('--accent-alt').trim();
            const yearKeys = Object.keys(yearlyData);
            const wasteArr = yearKeys.map(y => yearlyData[y].avgWaste);
            const marginArr = yearKeys.map(y => yearlyData[y].avgMargin);
            const wastePctArr = yearKeys.map(y => yearlyData[y].avgWastePercent);
            const baseWaste = wasteArr[0] || 1;
            const baseMargin = marginArr[0] || 1;
            const baseWastePct = wastePctArr[0] || 1;

            yearlyChart = new Chart(yearlyCtx, {
                type: 'bar',
                data: {
                    labels: yearKeys,
                    datasets: [
                        {
                            type:'bar',
                            label: 'Selejt HUF',
                            data: wasteArr,
                            backgroundColor: [accentStart, accentEnd, accentAlt],
                            borderRadius: 10,
                            borderSkipped: false,
                            yAxisID: 'y'
                        },
                        {
                            type:'line',
                            label: 'Árrés %',
                            data: marginArr,
                            borderColor: accentEnd,
                            backgroundColor: accentEnd + '33',
                            tension:.35,
                            pointRadius:5,
                            yAxisID: 'y2'
                        },
                        {
                            type:'line',
                            label: 'Selejt %',
                            data: wastePctArr,
                            borderColor: accentAlt,
                            backgroundColor: accentAlt + '33',
                            tension:.35,
                            pointRadius:5,
                            yAxisID: 'y2'
                        }
                    ]
                },
                options: {
                    responsive:true,
                    maintainAspectRatio:false,
                    interaction:{ mode:'nearest', intersect:true },
                    plugins:{
                        legend:{ display:false },
                        tooltip:{ enabled:false }
                    },
                    scales:{
                        y:{
                            beginAtZero:true,
                            ticks:{ callback:v=> new Intl.NumberFormat('hu-HU').format(v) + ' Ft' }
                        },
                        y2:{
                            position:'right',
                            beginAtZero:true,
                            grid:{ drawOnChartArea:false },
                            ticks:{ callback:v=> v.toFixed(1)+'%' }
                        }
                    },
                    onHover: (evt, activeEls) => { if(activeEls?.length){ updateYearInfo(activeEls[0].index); } }
                },
                plugins:[barValuePlugin, {
                    id:'pinClickYear',
                    afterEvent(chart,args){
                        const e = args.event; if(e.type!=='click') return;
                        const points = chart.getElementsAtEventForMode(e,'nearest',{intersect:true}, true);
                        if(!points.length) return;
                        const p = points[0];
                        const ds = chart.data.datasets[p.datasetIndex];
                        const value = ds.data[p.index];
                        const label = chart.data.labels[p.index];
                        const canvas = chart.canvas;
                        let cont = canvas.parentNode.querySelector('.pinned-tooltips');
                        if(!cont){ cont=document.createElement('div'); cont.className='pinned-tooltips'; Object.assign(cont.style,{position:'absolute',left:0,top:0,pointerEvents:'none'}); canvas.parentNode.style.position='relative'; canvas.parentNode.appendChild(cont); }
                        const el = chart.getDatasetMeta(p.datasetIndex).data[p.index];
                        const div=document.createElement('div');
                        Object.assign(div.style,{position:'absolute',left:el.x+'px',top:el.y+'px',transform:'translate(-50%,-110%)',background:'rgba(15,23,42,0.92)',backdropFilter:'blur(6px)',border:'1px solid var(--card-border)',borderRadius:'10px',padding:'6px 8px 8px',fontSize:'.65rem',color:'var(--text-color)',boxShadow:'0 6px 18px -6px rgba(0,0,0,0.6)',pointerEvents:'auto'});
                        const unit = p.datasetIndex===0? ' Ft' : '%';
                        div.innerHTML=`<button type="button" aria-label="Bezárás" style="position:absolute;top:2px;right:2px;background:transparent;border:none;color:var(--text-dim);cursor:pointer;font-size:.7rem;">×</button><div style="font-weight:600;white-space:nowrap;">${label}</div><div style="margin-top:2px;">${p.datasetIndex===0? new Intl.NumberFormat('hu-HU').format(value): value.toFixed(2)}${unit}</div>`;
                        div.querySelector('button').addEventListener('click',()=>div.remove());
                        cont.appendChild(div);
                    }
                }]
            });

            // Mode buttons
            const modeRawBtn = document.getElementById('modeRaw');
            const modeIndexBtn = document.getElementById('modeIndex');
            let mode = 'raw';
            function setMode(m){
                mode = m;
                modeRawBtn.classList.toggle('active', m==='raw');
                modeIndexBtn.classList.toggle('active', m==='index');
                document.getElementById('footerModeNote').textContent = 'Mód: ' + (m==='raw' ? 'Érték' : 'Index (bázis='+yearKeys[0]+')');
                updateYearInfo(currentYearIndex);
                renderYearSummaries(mode, yearlyData, yearKeys, { baseWaste, baseMargin, baseWastePct });
            }
            modeRawBtn.addEventListener('click', ()=> setMode('raw'));
            modeIndexBtn.addEventListener('click', ()=> setMode('index'));

            let currentYearIndex = 0;
            function updateYearInfo(idx){
                currentYearIndex = idx;
                const year = yearKeys[idx];
                if(!year) return;
                const d = yearlyData[year];
                const prev = yearlyData[yearKeys[idx-1]];
                const fmtWaste = v => formatHUF(Math.round(v));
                const fmtPct = v => v.toFixed(2)+'%';
                const getDelta = (now, prev) => {
                    if(!prev) return {txt:'-', cls:''};
                    const diff = now - prev;
                    const perc = (diff/prev)*100;
                    return { txt: (diff>0?'+':'')+ (mode==='raw'? (diff.toFixed(0)) : perc.toFixed(1)+'%'), cls: diff>0?'up':'down' };
                };
                let wasteVal = d.avgWaste;
                let marginVal = d.avgMargin;
                let wastePctVal = d.avgWastePercent;
                if(mode==='index'){
                    wasteVal = (wasteVal / baseWaste)*100;
                    marginVal = (marginVal / baseMargin)*100;
                    wastePctVal = (wastePctVal / baseWastePct)*100;
                }
                document.getElementById('infoYearLabel').textContent = 'Év: '+year;
                document.getElementById('infoAvgWaste').textContent = mode==='raw'? fmtWaste(wasteVal) : wasteVal.toFixed(1)+' i';
                document.getElementById('infoAvgMargin').textContent = mode==='raw'? marginVal.toFixed(1)+'%' : marginVal.toFixed(1)+' i';
                document.getElementById('infoAvgWastePct').textContent = mode==='raw'? wastePctVal.toFixed(2)+'%' : wastePctVal.toFixed(1)+' i';
                const dw = getDelta(d.avgWaste, prev?.avgWaste);
                const dm = getDelta(d.avgMargin, prev?.avgMargin);
                const dwp = getDelta(d.avgWastePercent, prev?.avgWastePercent);
                // Append currency unit for waste delta in raw mode
                if(mode==='raw' && dw.txt !== '-' && !dw.txt.endsWith('Ft')) { dw.txt = dw.txt + ' Ft'; }
                const setDelta = (id,delta)=>{ const el=document.getElementById(id); el.textContent=delta.txt; el.className='delta '+(delta.cls||''); };
                setDelta('deltaWaste', dw); setDelta('deltaMargin', dm); setDelta('deltaWastePct', dwp);
            }
            updateYearInfo(0);
            renderYearSummaries(mode, yearlyData, yearKeys, { baseWaste, baseMargin, baseWastePct });
        }

        function calculateYearlyAverages() {
            const yearlyData = {};
            
            activeData.forEach(row => {
                const year = row.date.getFullYear();
                if (!yearlyData[year]) {
                    yearlyData[year] = { wasteSum: 0, marginSum: 0, wastePercentSum: 0, count: 0 };
                }
                yearlyData[year].wasteSum += row.wasteHUF;
                yearlyData[year].marginSum += row.marginPercent;
                yearlyData[year].wastePercentSum += row.wastePercent;
                yearlyData[year].count++;
            });

            Object.keys(yearlyData).forEach(year => {
                const data = yearlyData[year];
                yearlyData[year] = {
                    avgWaste: data.wasteSum / data.count,
                    avgMargin: data.marginSum / data.count,
                    avgWastePercent: data.wastePercentSum / data.count
                };
            });

            return yearlyData;
        }

        function renderYearSummaries(mode, yearlyData, yearKeys, bases){
            const container = document.getElementById('yearSummaryList');
            if(!container) return;
            const { baseWaste, baseMargin, baseWastePct } = bases;
            container.innerHTML='';
            const years = yearKeys || Object.keys(yearlyData);
            years.forEach((year,i) => {
                const d = yearlyData[year];
                const prev = yearlyData[years[i-1]];
                const wasteValRaw = d.avgWaste;
                const marginValRaw = d.avgMargin;
                const wastePctRaw = d.avgWastePercent;
                const wasteVal = mode==='index' ? (wasteValRaw / baseWaste)*100 : wasteValRaw;
                const marginVal = mode==='index' ? (marginValRaw / baseMargin)*100 : marginValRaw;
                const wastePctVal = mode==='index' ? (wastePctRaw / baseWastePct)*100 : wastePctRaw;
                // Always show raw HUF value with Ft for clarity (even index mode)
                const fmtWaste = () => formatHUF(Math.round(wasteValRaw));
                const fmtPct = v => (mode==='index'? v.toFixed(1)+' i' : v.toFixed(2)+'%');
                const idxWaste = (wasteValRaw / baseWaste)*100;
                const idxMargin = (marginValRaw / baseMargin)*100;
                const idxWastePct = (wastePctRaw / baseWastePct)*100;
                const delta = (now, prev) => {
                    if(!prev) return { cls:'', txt:'-' };
                    const diff = now - prev;
                    const cls = diff>0?'up':'down';
                    const baseTxt = (diff>0?'+':'') + (mode==='index'? ( (diff/prev*100).toFixed(1)+'%' ) : diff.toFixed(0));
                    return { cls, txt: mode==='raw' ? baseTxt + ' Ft' : baseTxt };
                };
                const dW = delta(d.avgWaste, prev?.avgWaste);
                const dM = delta(d.avgMargin, prev?.avgMargin);
                const dWP = delta(d.avgWastePercent, prev?.avgWastePercent);
                const item = document.createElement('div');
                item.className = 'year-summary-item';
                item.innerHTML = `
                   <div class="ysi-header">
                       <div class="ysi-year">${year}</div>
                       <div class="ysi-index-badge">Idx W:${idxWaste.toFixed(0)} / M:${idxMargin.toFixed(0)} / S:${idxWastePct.toFixed(0)}</div>
                   </div>
                   <div class="ysi-grid">
                       <div class="ysi-block">
                           <label>Selejt HUF</label>
                           <div class="ysi-val">${fmtWaste()}</div>
                           <div class="ysi-idx">Index: ${idxWaste.toFixed(1)}</div>
                           <div class="ysi-delta ${dW.cls}">${dW.txt}</div>
                       </div>
                       <div class="ysi-block">
                           <label>Árrés %</label>
                           <div class="ysi-val">${mode==='index'? marginVal.toFixed(1)+' i' : marginVal.toFixed(1)+'%'}</div>
                           <div class="ysi-idx">Index: ${idxMargin.toFixed(1)}</div>
                           <div class="ysi-delta ${dM.cls}">${dM.txt}</div>
                       </div>
                       <div class="ysi-block">
                           <label>Selejt %</label>
                           <div class="ysi-val">${fmtPct(wastePctVal)}</div>
                           <div class="ysi-idx">Index: ${idxWastePct.toFixed(1)}</div>
                           <div class="ysi-delta ${dWP.cls}">${dWP.txt} </div>
                       </div>
                   </div>`;
                container.appendChild(item);
            });
        }

        // ====== VARIANT SUMMARY CARDS ==================================================
        function buildYearlyMetrics(){
            // Build extended metrics (sum, min, max, yoy, monthly series per year)
            const metrics = {}; // year -> data
            activeData.forEach(r => {
                const y = r.date.getFullYear();
                if(!metrics[y]) metrics[y] = { months: [], wasteSum:0, marginSum:0, wastePctSum:0, wasteMin:Infinity, wasteMax:-Infinity };
                const m = metrics[y];
                m.months.push(r);
                m.wasteSum += r.wasteHUF;
                m.marginSum += r.marginPercent;
                m.wastePctSum += r.wastePercent;
                if(r.wasteHUF < m.wasteMin) m.wasteMin = r.wasteHUF;
                if(r.wasteHUF > m.wasteMax) m.wasteMax = r.wasteHUF;
            });
            const years = Object.keys(metrics).sort();
            const baseYear = years[0];
            const base = metrics[baseYear];
            years.forEach((y,i) => {
                const d = metrics[y];
                d.year = y;
                d.count = d.months.length;
                d.avgWaste = d.wasteSum / d.count;
                d.avgMargin = d.marginSum / d.count;
                d.avgWastePct = d.wastePctSum / d.count;
                // indexes vs base averages
                d.idxWaste = (d.avgWaste / (base.avgWaste || 1))*100;
                d.idxMargin = (d.avgMargin / (base.avgMargin || 1))*100;
                d.idxWastePct = (d.avgWastePct / (base.avgWastePct || 1))*100;
                // YoY vs previous year averages
                if(i>0){
                    const p = metrics[years[i-1]];
                    d.yoyWasteDiff = d.avgWaste - p.avgWaste;
                    d.yoyWastePct = p.avgWaste ? (d.yoyWasteDiff / p.avgWaste)*100 : 0;
                    d.yoyMarginDiff = d.avgMargin - p.avgMargin;
                    d.yoyMarginPct = p.avgMargin ? (d.yoyMarginDiff / p.avgMargin)*100 : 0;
                    d.yoyWastePctDiff = d.avgWastePct - p.avgWastePct;
                    d.yoyWastePctPct = p.avgWastePct ? (d.yoyWastePctDiff / p.avgWastePct)*100 : 0;
                    // additional yoy for margin% and waste% (already computed above but keep naming clarity)
                    d.yoyMarginPctDiff = d.yoyMarginDiff; // absolute diff points
                    d.yoyWastePercentPctDiff = d.yoyWastePctDiff; // absolute diff points
                } else {
                    d.yoyWasteDiff = d.yoyWastePct = d.yoyMarginDiff = d.yoyMarginPct = d.yoyWastePctDiff = d.yoyWastePctPct = d.yoyMarginPctDiff = d.yoyWastePercentPctDiff = null;
                }
            });
            return { metrics, years };
        }

        function fmtFt(v){ return formatHUF(Math.round(v)); }
        function signFmt(v, suffix=''){ if(v==null) return '-'; const s = v>0?'+':''; return s + (Math.abs(v) >= 1? Math.round(v).toString(): v.toFixed(2)) + suffix; }
        function signPct(v){ if(v==null) return '-'; const s=v>0?'+':''; return s+v.toFixed(1)+'%'; }
    function diffPctFmt(v){ if(v==null) return '-'; const s=v>0?'+':''; return s + Math.abs(v).toFixed(2) + '%'; }

        function renderVariant1(data){
            const { metrics, years } = data;
            const container = document.getElementById('variantContent');
            container.innerHTML = '';
            const wrap = document.createElement('div');
            wrap.className = 'variant1-list';
            const header = document.createElement('div');
            header.className='v1-row v1-head';
            header.innerHTML = '<div>Év</div><div>Átl. Selejt</div><div>Idx</div><div>Átl. Árrés %</div><div>Árrés Idx</div><div>Átl. Selejt %</div><div>Selejt% Idx</div><div>YoY Selejt</div><div>YoY %</div><div>YoY Árrés %</div><div>YoY Selejt %</div>';
            wrap.appendChild(header);
            years.forEach(y => {
                const d = metrics[y];
                const row = document.createElement('div');
                row.className='v1-row';
                     const clsWasteFt = d.yoyWasteDiff==null?'' : (d.yoyWasteDiff>=0?'pos':'neg');
                     const clsWastePct = d.yoyWastePct==null?'' : (d.yoyWastePct>=0?'pos':'neg');
                     const clsMarginPct = d.yoyMarginDiff==null?'' : (d.yoyMarginDiff>=0?'pos':'neg');
                    const clsWastePercPts = d.yoyWastePctDiff==null?'' : (d.yoyWastePctDiff<=0?'pos':'neg'); // lower waste% is good
                     row.innerHTML = `
                         <div>${y}</div>
                         <div>${fmtFt(d.avgWaste)}</div>
                         <div>${d.idxWaste.toFixed(1)}</div>
                         <div>${d.avgMargin.toFixed(1)}%</div>
                         <div>${d.idxMargin.toFixed(1)}</div>
                         <div>${d.avgWastePct.toFixed(2)}%</div>
                         <div>${d.idxWastePct.toFixed(1)}</div>
                         <div class="${clsWasteFt}">${d.yoyWasteDiff==null?'-':signFmt(d.yoyWasteDiff,' Ft')}</div>
                         <div class="${clsWastePct}">${d.yoyWastePct==null?'-':signPct(d.yoyWastePct)}</div>
                         <div class="${clsMarginPct}">${d.yoyMarginDiff==null?'-':diffPctFmt(d.yoyMarginDiff)}</div>
                           <div class="${clsWastePercPts}">${d.yoyWastePctDiff==null?'-':diffPctFmt(d.yoyWastePctDiff)}</div>`;
                wrap.appendChild(row);
            });
            container.appendChild(wrap);
        }

        function renderVariant2(data){
            const { metrics, years } = data;
            const container = document.getElementById('variantContent');
            container.innerHTML = '';
            const grid = document.createElement('div');
            grid.className='v2-grid';
            years.forEach(y => {
                const d = metrics[y];
                const card = document.createElement('div');
                card.className='v2-card';
                const wasteBarPct = Math.min(100, (d.idxWaste/Math.max(...years.map(yy=>metrics[yy].idxWaste)))*100);
                const marginBarPct = Math.min(100, (d.idxMargin/Math.max(...years.map(yy=>metrics[yy].idxMargin)))*100);
                const wastePctBarPct = Math.min(100, (d.idxWastePct/Math.max(...years.map(yy=>metrics[yy].idxWastePct)))*100);
          const clsWasteFt = d.yoyWasteDiff==null?'' : (d.yoyWasteDiff>=0?'pos':'neg');
          const clsWastePct = d.yoyWastePct==null?'' : (d.yoyWastePct>=0?'pos':'neg');
          const clsMarginPct = d.yoyMarginDiff==null?'' : (d.yoyMarginDiff>=0?'pos':'neg');
          const clsWastePercPts = d.yoyWastePctDiff==null?'' : (d.yoyWastePctDiff<=0?'pos':'neg');
          card.innerHTML = `
                   <div class="v2-head"><span>${y}</span><span class="v2-badge">Idx ${d.idxWaste.toFixed(0)}</span></div>
                   <div class="v2-section">
                       <label>Selejt (átl.)</label>
                       <div class="v2-val">${fmtFt(d.avgWaste)}</div>
                       <div class="v2-sub">Min: ${fmtFt(d.wasteMin)} • Max: ${fmtFt(d.wasteMax)}</div>
                       <div class="v2-bars"><div class="v2-bar" style="--p:${wasteBarPct}%;"></div></div>
              <div class="v2-delta">YoY: <span class="${clsWasteFt}">${d.yoyWasteDiff==null?'-':signFmt(d.yoyWasteDiff,' Ft')}</span> (<span class="${clsWastePct}">${d.yoyWastePct==null?'-':signPct(d.yoyWastePct)}</span>)</div>
                   </div>
                   <div class="v2-split">
                      <div>
                        <label>Árrés % (átl.)</label>
               <div class="v2-val-sm">${d.avgMargin.toFixed(1)}% <span class="muted">(Idx ${d.idxMargin.toFixed(0)})</span></div>
               <div class="v2-delta" style="margin-top:4px;">YoY: <span class="${clsMarginPct}">${d.yoyMarginDiff==null?'-':diffPctFmt(d.yoyMarginDiff)}</span></div>
                        <div class="v2-bars small"><div class="v2-bar" style="--p:${marginBarPct}%;"></div></div>
                      </div>
                      <div>
                        <label>Selejt % (átl.)</label>
               <div class="v2-val-sm">${d.avgWastePct.toFixed(2)}% <span class="muted">(Idx ${d.idxWastePct.toFixed(0)})</span></div>
               <div class="v2-delta" style="margin-top:4px;">YoY: <span class="${clsWastePercPts}">${d.yoyWastePctDiff==null?'-':diffPctFmt(d.yoyWastePctDiff)}</span></div>
                        <div class="v2-bars small"><div class="v2-bar" style="--p:${wastePctBarPct}%;"></div></div>
                      </div>
                   </div>
                   <div class="v2-footer">Hónapok: ${d.count}</div>`;
                grid.appendChild(card);
            });
            container.appendChild(grid);
        }

        function renderVariant3(data){
            const { metrics, years } = data;
            const container = document.getElementById('variantContent');
            container.innerHTML = '';
            const table = document.createElement('div');
            table.className='v3-matrix';
            // header
            let html = '<div class="v3-row v3-head"><div>Év</div><div>Selejt Átl.</div><div>Idx</div><div>YoY Ft</div><div>YoY %</div><div>Árrés Átl.%</div><div>Idx</div><div>YoY Árrés %</div><div>Selejt % Átl.</div><div>Idx</div><div>YoY Selejt %</div></div>';
            years.forEach(y => {
                const d = metrics[y];
                const clsWasteFt = d.yoyWasteDiff==null?'' : (d.yoyWasteDiff>=0?'pos':'neg');
                const clsWastePct = d.yoyWastePct==null?'' : (d.yoyWastePct>=0?'pos':'neg');
                const clsMarginPct = d.yoyMarginDiff==null?'' : (d.yoyMarginDiff>=0?'pos':'neg');
                const clsWastePercPts = d.yoyWastePctDiff==null?'' : (d.yoyWastePctDiff<=0?'pos':'neg');
                html += `<div class="v3-row">
                    <div>${y}</div>
                    <div>${fmtFt(d.avgWaste)}</div>
                    <div data-hm="${d.idxWaste.toFixed(1)}">${d.idxWaste.toFixed(1)}</div>
                    <div class="${clsWasteFt}">${d.yoyWasteDiff==null?'-':signFmt(d.yoyWasteDiff,' Ft')}</div>
                    <div class="${clsWastePct}">${d.yoyWastePct==null?'-':signPct(d.yoyWastePct)}</div>
                    <div>${d.avgMargin.toFixed(1)}%</div>
                    <div data-hm="${d.idxMargin.toFixed(1)}">${d.idxMargin.toFixed(1)}</div>
                    <div class="${clsMarginPct}">${d.yoyMarginDiff==null?'-':diffPctFmt(d.yoyMarginDiff)}</div>
                    <div>${d.avgWastePct.toFixed(2)}%</div>
                    <div data-hm="${d.idxWastePct.toFixed(1)}">${d.idxWastePct.toFixed(1)}</div>
                    <div class="${clsWastePercPts}">${d.yoyWastePctDiff==null?'-':diffPctFmt(d.yoyWastePctDiff)}</div>
                </div>`;
            });
            table.innerHTML = html;
            container.appendChild(table);
            // Heatmap coloring based on data-hm attributes
            const cells = table.querySelectorAll('[data-hm]');
            const vals = Array.from(cells).map(c=>parseFloat(c.getAttribute('data-hm')));
            const min = Math.min(...vals); const max = Math.max(...vals);
            cells.forEach(c => {
                const v = parseFloat(c.getAttribute('data-hm'));
                const ratio = (v - min) / (max - min || 1);
                c.style.background = `linear-gradient(90deg, var(--card-bg) 0%, rgba(99,102,241,${0.15 + 0.55*ratio}) 100%)`;
                c.style.borderRadius = '6px';
            });
        }

        function initVariants(){
            const switcher = document.getElementById('variantSwitch');
            if(!switcher) return;
            const data = buildYearlyMetrics();
            const buttons = switcher.querySelectorAll('.variant-btn');
            function setActive(v){
                buttons.forEach(b=> b.classList.toggle('active', b.dataset.variant===v));
                if(v==='1') renderVariant1(data);
                else if(v==='2') renderVariant2(data);
                else renderVariant3(data);
            }
            switcher.addEventListener('click', e => {
                const btn = e.target.closest('.variant-btn');
                if(!btn) return;
                setActive(btn.dataset.variant);
            });
            setActive('1');
        }

        // ==============================================================================

        // === Enhanced trend interaktív vezérlés ===
        function calcMovingAverage(arr, len){ if(len<=1) return null; const out=[]; for(let i=0;i<arr.length;i++){ if(i+1<len){ out.push(null); continue;} let sum=0; for(let k=i-len+1;k<=i;k++) sum+=arr[k]; out.push(sum/len);} return out; }
        function ensureTrendDatasets(){
            if(!trendChart) return;
            // base dataset index 0 always current metric
            if(trendChart.data.datasets.length<1){
                trendChart.data.datasets = [{label:'', data:[], borderWidth:3, tension:.4, fill:true, pointRadius:5 }];
            }
            // moving average dataset at index 1
            if(trendChart.data.datasets.length<2){
                trendChart.data.datasets.push({label:'MA', data:[], borderColor:'var(--accent-alt)', borderWidth:2, pointRadius:0, tension:.3, fill:false, borderDash:[6,4]});
            }
        }
        function setTrendMetric(metric){
            ensureTrendDatasets();
            const accentStart = getComputedStyle(root).getPropertyValue('--accent-start').trim();
            const accentEnd = getComputedStyle(root).getPropertyValue('--accent-end').trim();
            let data,label,isPercent=false;
            if(metric==='waste'){ data = activeData.map(d=>d.wasteHUF); label='Selejt HUF'; }
            else if(metric==='margin'){ data = activeData.map(d=>d.marginPercent); label='Árrés %'; isPercent=true; }
            else { data = activeData.map(d=>d.wastePercent); label='Selejt %'; isPercent=true; }
            // gradient background for fill
            const ctx = trendChart.ctx; const grad=ctx.createLinearGradient(0,0,0,300); grad.addColorStop(0,accentStart+'EE'); grad.addColorStop(1,accentEnd+'22');
            const ds = trendChart.data.datasets[0];
            ds.data = data;
            ds.label = label;
            ds.borderColor = accentStart;
            ds.pointBackgroundColor = accentStart;
            ds.backgroundColor = grad;
            trendChart.options.scales.y.ticks.callback = v => isPercent? v.toFixed(1)+'%' : new Intl.NumberFormat('hu-HU').format(v)+' Ft';
            applyMovingAverage();
            trendChart.update();
        }
        function applyMovingAverage(){
            const sel = document.getElementById('maSelect'); if(!sel || !trendChart) return; const len=parseInt(sel.value,10); ensureTrendDatasets();
            const base = trendChart.data.datasets[0].data; const ma = calcMovingAverage(base,len);
            const maDs = trendChart.data.datasets[1];
            if(!ma || len===0){ maDs.data = []; maDs.hidden=true; }
            else { maDs.data = ma; maDs.hidden=false; maDs.label = 'MA '+len; }
        }
        function initTrendControls(){
            const controls = document.getElementById('trendControls'); if(!controls) return;
            controls.addEventListener('click', e=>{
                const b = e.target.closest('.mt-btn'); if(!b) return;
                controls.querySelectorAll('.mt-btn').forEach(x=> x.classList.toggle('active', x===b));
                setTrendMetric(b.dataset.metric);
            });
            const maSel = document.getElementById('maSelect'); maSel?.addEventListener('change', ()=>{ applyMovingAverage(); trendChart.update(); });
            const clearBtn = document.getElementById('clearPins'); clearBtn?.addEventListener('click', ()=>{ document.querySelectorAll('.pinned-tip').forEach(n=>n.remove()); });
        }

        function setupSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const filteredData = activeData.filter(row => 
                    row.dateString.toLowerCase().includes(searchTerm) ||
                    row.wasteHUF.toString().includes(searchTerm) ||
                    row.marginPercent.toFixed(1).includes(searchTerm) ||
                    row.wastePercent.toFixed(2).includes(searchTerm)
                );
                renderTable(filteredData);
            });
        }

        // ===== DATE FILTER =====================================================
        function initDateFilterUI(){
            const panel = document.getElementById('dataPanel') || document;
            const fromEl = panel.querySelector('#dateFrom');
            const toEl = panel.querySelector('#dateTo');
            const applyBtn = panel.querySelector('#applyDateFilter');
            const badge = panel.querySelector('#dateFilterBadge');
            const presetButtons = panel.querySelectorAll('.preset-btn[data-preset]');
            applyBtn?.addEventListener('click', ()=>{ applyDateInputs(); });
            presetButtons.forEach(btn=> btn.addEventListener('click', ()=>{ selectPreset(btn.dataset.preset); }));
            updateDateBadge();
            // Pre-populate input values
            if(currentDateFilter){
                if(currentDateFilter.from) fromEl.value = isoInput(currentDateFilter.from);
                if(currentDateFilter.to) toEl.value = isoInput(currentDateFilter.to);
            }
        }

        function isoInput(d){ return d.toISOString().slice(0,10); }

        function loadStoredDateFilter(){
            try{ const stored = JSON.parse(localStorage.getItem(DATE_FILTER_KEY)||'null'); if(stored){
                currentDateFilter = {
                    from: stored.from? new Date(stored.from): null,
                    to: stored.to? new Date(stored.to): null,
                    preset: stored.preset||null
                };
            }}catch(e){/* ignore */}
        }

        function storeDateFilter(){
            if(!currentDateFilter){ localStorage.removeItem(DATE_FILTER_KEY); return; }
            localStorage.setItem(DATE_FILTER_KEY, JSON.stringify({
                from: currentDateFilter.from? currentDateFilter.from.toISOString(): null,
                to: currentDateFilter.to? currentDateFilter.to.toISOString(): null,
                preset: currentDateFilter.preset||null
            }));
        }

        function applyDateInputs(){
            const fromEl = document.getElementById('dateFrom');
            const toEl = document.getElementById('dateTo');
            const from = fromEl.value? new Date(fromEl.value): null;
            const to = toEl.value? new Date(toEl.value): null;
            currentDateFilter = { from, to, preset: null };
            applyActiveFilter();
        }

        function selectPreset(preset){
            const all = [...fullData];
            const sorted = all.slice().sort((a,b)=> a.date - b.date);
            const last = sorted[sorted.length-1];
            let from=null, to=null;
            switch(preset){
                case 'full':
                    from = sorted[0].date; to = last.date; break;
                case 'ytd':
                    const yearStart = new Date(last.date.getFullYear(),0,1);
                    from = yearStart; to = last.date; break;
                case 'last3': case 'last6': case 'last12':
                    const n = parseInt(preset.replace('last','')); // months
                    to = last.date;
                    const idxFrom = Math.max(0, sorted.length - n);
                    from = sorted[idxFrom].date; break;
            }
            currentDateFilter = { from, to, preset };
            // update inputs visually
            const fromEl = document.getElementById('dateFrom');
            const toEl = document.getElementById('dateTo');
            if(fromEl) fromEl.value = isoInput(from);
            if(toEl) toEl.value = isoInput(to);
            highlightPreset(preset);
            applyActiveFilter();
        }

        function highlightPreset(p){
            (document.getElementById('dataPanel')||document).querySelectorAll('.preset-btn[data-preset]').forEach(b=>{
                b.classList.toggle('active', b.dataset.preset===p);
            });
        }

        function applyActiveFilter(rebuild=true){
            if(!currentDateFilter){ activeData=[...fullData]; }
            else {
                activeData = fullData.filter(r => {
                    const time = r.date.getTime();
                    if(currentDateFilter.from && time < currentDateFilter.from.getTime()) return false;
                    if(currentDateFilter.to && time > currentDateFilter.to.getTime()) return false;
                    return true;
                });
            }
            storeDateFilter();
            updateDateBadge();
            calculateStats();
            renderTable(activeData);
            if(rebuild){
                // Rebuild charts & yearly metrics
                if(trendChart){ trendChart.destroy(); }
                if(yearlyChart){ yearlyChart.destroy(); }
                initCharts();
                if(typeof initVariants==='function') initVariants();
            }
        }

        function updateDateBadge(){
            const badge = (document.getElementById('dataPanel')||document).querySelector('#dateFilterBadge');
            if(!badge) return;
            if(!currentDateFilter){ badge.style.display='none'; return; }
            const fmt = d=> d.toLocaleDateString('hu-HU',{year:'2-digit', month:'2-digit', day:'2-digit'});
            badge.style.display='inline-block';
            let label='';
            if(currentDateFilter.preset){
                const map={ytd:'YTD', last3:'Utolsó 3', last6:'Utolsó 6', last12:'Utolsó 12', full:'Teljes'};
                label = map[currentDateFilter.preset] || currentDateFilter.preset;
            } else {
                label = (currentDateFilter.from? fmt(currentDateFilter.from):'…') + ' - ' + (currentDateFilter.to? fmt(currentDateFilter.to):'…');
            }
            badge.textContent = label + ' ('+activeData.length+' hó)';
            updateFilterToggleState();
        }

        // Data panel logic
        function initDataPanel(){
            const toggle = document.getElementById('dataPanelToggle');
            const panel = document.getElementById('dataPanel');
            const closeBtn = panel.querySelector('.close-btn');
            if(!toggle || !panel) return;
            toggle.addEventListener('click', ()=> panel.classList.toggle('open'));
            closeBtn.addEventListener('click', ()=> panel.classList.remove('open'));
            document.addEventListener('keydown', e=>{ if(e.key==='Escape') panel.classList.remove('open'); });
            document.addEventListener('click', e=>{
                if(!panel.classList.contains('open')) return;
                if(panel.contains(e.target) || e.target===toggle) return;
                panel.classList.remove('open');
            });
            const resetBtn = document.getElementById('resetDateFilter');
            resetBtn?.addEventListener('click', ()=>{ currentDateFilter=null; applyActiveFilter(); highlightPreset(null); const from=document.getElementById('dateFrom'); const to=document.getElementById('dateTo'); if(from) from.value=''; if(to) to.value=''; });
            updateFilterToggleState();
        }

        function updateFilterToggleState(){
            const toggle = document.getElementById('dataPanelToggle');
            if(!toggle) return;
            const full = activeData.length === fullData.length;
            toggle.classList.toggle('active-filter', !full);
        }

        // Initialize when page loads
    document.addEventListener('DOMContentLoaded', initDashboard);
    document.addEventListener('DOMContentLoaded', initVariants);
    </script>
    <button class="data-panel-toggle" id="dataPanelToggle">📋 Adatok</button>
    <div class="data-panel" id="dataPanel" role="dialog" aria-labelledby="dataPanelTitle">
        <h3 id="dataPanelTitle">Részletes Adatok</h3>
        <button class="close-btn" type="button" aria-label="Bezárás">×</button>
        <div class="panel-section">
            <span class="sec-label">Dátumtartomány</span>
            <div class="range-row">
                <input type="date" id="dateFrom">
                <input type="date" id="dateTo">
            </div>
        </div>
        <div class="panel-section">
            <span class="sec-label">Presetek</span>
            <div class="preset-grid">
                <button type="button" class="preset-btn" data-preset="ytd">YTD</button>
                <button type="button" class="preset-btn" data-preset="last3">Utolsó 3</button>
                <button type="button" class="preset-btn" data-preset="last6">Utolsó 6</button>
                <button type="button" class="preset-btn" data-preset="last12">Utolsó 12</button>
                <button type="button" class="preset-btn" data-preset="full">Teljes</button>
            </div>
        </div>
        <div class="panel-actions">
            <button type="button" id="applyDateFilter" class="primary">Alkalmaz</button>
            <button type="button" id="resetDateFilter" class="ghost">Reset</button>
        </div>
        <div class="panel-badge"><span id="dateFilterBadge"></span></div>
        <div class="panel-section">
            <span class="sec-label">Keresés</span>
            <input type="text" id="searchInput" placeholder="Keresés...">
        </div>
    </div>
</body>
</html>