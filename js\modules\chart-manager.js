/**
 * ===================================================================
 * CHART MANAGER MODULE
 * Handles Chart.js instances and chart-related functionality
 * ===================================================================
 */

/**
 * Chart Manager Class
 * Manages all chart instances and their configurations
 */
export class ChartManager {
    constructor(dataManager, themeManager) {
        this.dataManager = dataManager;
        this.themeManager = themeManager;
        this.charts = new Map();
        this.pinnedTooltips = new Map();
        this.currentTrendMetric = 'waste';
        this.movingAverageWindow = 0;
        
        this.init();
    }

    /**
     * Initialize chart manager
     */
    init() {
        this.setupEventListeners();
        this.registerChartPlugins();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for data changes
        this.dataManager.addEventListener('dataChanged', (data) => {
            this.updateAllCharts(data.activeData);
        });

        // Listen for theme changes
        document.addEventListener('themechange', () => {
            this.updateChartColors();
        });
    }

    /**
     * Register custom Chart.js plugins
     */
    registerChartPlugins() {
        // Plugin for pinned tooltips
        Chart.register({
            id: 'pinnedTooltips',
            afterEvent: (chart, args) => {
                const event = args.event;
                if (event.type === 'click') {
                    this.handleChartClick(chart, event);
                }
            }
        });

        // Plugin for bar value labels
        Chart.register({
            id: 'barValueLabels',
            afterDatasetsDraw: (chart) => {
                if (chart.canvas.id === 'yearlyChart') {
                    this.drawBarValueLabels(chart);
                }
            }
        });
    }

    /**
     * Create trend chart
     */
    createTrendChart(canvasId, data) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        const colors = this.getCurrentColors();

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(d => d.dateString),
                datasets: [{
                    label: 'Selejt HUF',
                    data: data.map(d => d.wasteHUF),
                    borderColor: colors.accent.start,
                    backgroundColor: this.createGradient(ctx, colors.accent.start, colors.accent.end),
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: colors.accent.start,
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: true }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        ticks: {
                            callback: (value) => this.dataManager.formatHUF(value)
                        }
                    },
                    x: {
                        ticks: { maxRotation: 45 }
                    }
                },
                elements: {
                    point: { hoverRadius: 8 }
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    /**
     * Create yearly comparison chart
     */
    createYearlyChart(canvasId, yearlyData) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        const colors = this.getCurrentColors();
        
        const years = Object.keys(yearlyData);
        const wasteData = years.map(year => yearlyData[year].avgWaste);
        const marginData = years.map(year => yearlyData[year].avgMargin);
        const wastePctData = years.map(year => yearlyData[year].avgWastePercent);

        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: years,
                datasets: [
                    {
                        type: 'bar',
                        label: 'Selejt HUF',
                        data: wasteData,
                        backgroundColor: [colors.accent.start, colors.accent.end, colors.accent.alt],
                        borderRadius: 10,
                        borderSkipped: false,
                        yAxisID: 'y'
                    },
                    {
                        type: 'line',
                        label: 'Árrés %',
                        data: marginData,
                        borderColor: colors.accent.end,
                        backgroundColor: colors.accent.end + '33',
                        tension: 0.35,
                        pointRadius: 5,
                        yAxisID: 'y2'
                    },
                    {
                        type: 'line',
                        label: 'Selejt %',
                        data: wastePctData,
                        borderColor: colors.accent.alt,
                        backgroundColor: colors.accent.alt + '33',
                        tension: 0.35,
                        pointRadius: 5,
                        yAxisID: 'y2'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: { mode: 'nearest', intersect: true },
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.dataManager.formatHUF(value)
                        }
                    },
                    y2: {
                        position: 'right',
                        beginAtZero: true,
                        grid: { drawOnChartArea: false },
                        ticks: {
                            callback: (value) => value.toFixed(1) + '%'
                        }
                    }
                }
            }
        });

        this.charts.set(canvasId, chart);
        return chart;
    }

    /**
     * Create mini chart
     */
    createMiniChart(canvasId, data, type = 'line') {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        const colors = this.getCurrentColors();
        
        const labels = data.map((_, index) => index.toString());
        const minMax = this.getMinMax(data);

        const config = {
            type: type,
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    borderWidth: type === 'line' ? 2 : 0,
                    pointRadius: 0,
                    tension: 0.35,
                    fill: type === 'line',
                    backgroundColor: type === 'bar' ? 
                        colors.accent.start + '55' : 
                        colors.accent.start + '33',
                    borderColor: colors.accent.start,
                    borderRadius: type === 'bar' ? 3 : 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    x: { display: false },
                    y: {
                        display: true,
                        grid: { display: false },
                        ticks: {
                            maxTicksLimit: 3,
                            callback: (value) => {
                                return Math.round(value) >= 1000 ? 
                                    Math.round(value / 1000) + 'k' : 
                                    Math.round(value);
                            }
                        },
                        suggestedMin: minMax.min,
                        suggestedMax: minMax.max
                    }
                },
                layout: { padding: { top: 2, bottom: 0, left: 0, right: 0 } }
            }
        };

        const chart = new Chart(ctx, config);
        this.charts.set(canvasId, chart);
        return chart;
    }

    /**
     * Update trend chart metric
     */
    setTrendMetric(metric) {
        this.currentTrendMetric = metric;
        const chart = this.charts.get('trendChart');
        if (!chart) return;

        const data = this.dataManager.getActiveData();
        const colors = this.getCurrentColors();
        
        let chartData, label, isPercent = false;
        
        switch (metric) {
            case 'waste':
                chartData = data.map(d => d.wasteHUF);
                label = 'Selejt HUF';
                break;
            case 'margin':
                chartData = data.map(d => d.marginPercent);
                label = 'Árrés %';
                isPercent = true;
                break;
            case 'wastePct':
                chartData = data.map(d => d.wastePercent);
                label = 'Selejt %';
                isPercent = true;
                break;
        }

        const gradient = this.createGradient(
            chart.ctx, 
            colors.accent.start, 
            colors.accent.end
        );

        chart.data.datasets[0].data = chartData;
        chart.data.datasets[0].label = label;
        chart.data.datasets[0].borderColor = colors.accent.start;
        chart.data.datasets[0].pointBackgroundColor = colors.accent.start;
        chart.data.datasets[0].backgroundColor = gradient;

        chart.options.scales.y.ticks.callback = isPercent ? 
            (value) => value.toFixed(1) + '%' : 
            (value) => this.dataManager.formatHUF(value);

        this.applyMovingAverage(chart);
        chart.update();
    }

    /**
     * Apply moving average to trend chart
     */
    applyMovingAverage(chart) {
        if (!chart) chart = this.charts.get('trendChart');
        if (!chart) return;

        // Ensure we have a moving average dataset
        if (chart.data.datasets.length < 2) {
            const colors = this.getCurrentColors();
            chart.data.datasets.push({
                label: 'MA',
                data: [],
                borderColor: colors.accent.alt,
                borderWidth: 2,
                pointRadius: 0,
                tension: 0.3,
                fill: false,
                borderDash: [6, 4]
            });
        }

        const baseData = chart.data.datasets[0].data;
        const maData = this.dataManager.calculateMovingAverage(baseData, this.movingAverageWindow);
        const maDataset = chart.data.datasets[1];

        if (!maData || this.movingAverageWindow === 0) {
            maDataset.data = [];
            maDataset.hidden = true;
        } else {
            maDataset.data = maData;
            maDataset.hidden = false;
            maDataset.label = `MA ${this.movingAverageWindow}`;
        }

        chart.update();
    }

    /**
     * Set moving average window
     */
    setMovingAverageWindow(window) {
        this.movingAverageWindow = parseInt(window, 10);
        this.applyMovingAverage();
    }

    /**
     * Handle chart click for pinned tooltips
     */
    handleChartClick(chart, event) {
        const points = chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, true);
        if (points.length) {
            this.addPinnedTooltip(chart, points[0]);
        }
    }

    /**
     * Add pinned tooltip
     */
    addPinnedTooltip(chart, point) {
        const canvas = chart.canvas;
        const container = this.getTooltipContainer(canvas);
        const id = Date.now() + Math.random();
        
        const value = point.element.$context?.raw ?? point.raw;
        const label = chart.data.labels[point.index];
        
        const div = document.createElement('div');
        div.className = 'pinned-tooltip';
        div.style.cssText = `
            position: absolute;
            left: ${point.element.x}px;
            top: ${point.element.y}px;
            transform: translate(-50%, -110%);
            background: rgba(15, 23, 42, 0.92);
            backdrop-filter: blur(6px);
            border: 1px solid var(--card-border);
            border-radius: 10px;
            padding: 6px 8px 8px;
            font-size: 0.65rem;
            line-height: 1.25;
            color: var(--text-color);
            box-shadow: 0 6px 18px -6px rgba(0, 0, 0, 0.6);
            pointer-events: auto;
            z-index: 1000;
        `;
        
        div.innerHTML = `
            <button type="button" aria-label="Bezárás" style="position:absolute;top:2px;right:2px;background:transparent;border:none;color:var(--text-dim);cursor:pointer;font-size:0.7rem;">×</button>
            <div style="font-weight:600;white-space:nowrap;">${label}</div>
            <div style="margin-top:2px;">${this.formatTooltipValue(value)}</div>
        `;
        
        div.querySelector('button').addEventListener('click', () => {
            div.remove();
            this.pinnedTooltips.delete(id);
        });
        
        container.appendChild(div);
        this.pinnedTooltips.set(id, div);
    }

    /**
     * Clear all pinned tooltips
     */
    clearPinnedTooltips() {
        this.pinnedTooltips.forEach(tooltip => tooltip.remove());
        this.pinnedTooltips.clear();
    }

    /**
     * Get or create tooltip container
     */
    getTooltipContainer(canvas) {
        let container = canvas.parentNode.querySelector('.pinned-tooltips');
        if (!container) {
            container = document.createElement('div');
            container.className = 'pinned-tooltips';
            container.style.cssText = 'position:absolute;left:0;top:0;pointer-events:none;';
            canvas.parentNode.style.position = 'relative';
            canvas.parentNode.appendChild(container);
        }
        return container;
    }

    /**
     * Draw bar value labels
     */
    drawBarValueLabels(chart) {
        const { ctx } = chart;
        ctx.save();
        
        const textColor = document.body.classList.contains('facts-mode') ? 
            '#000' : 
            getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim() || '#e2e8f0';
        
        const dataset = chart.data.datasets[0];
        chart.getDatasetMeta(0).data.forEach((bar, index) => {
            const value = dataset.data[index];
            if (value == null) return;
            
            const formatted = this.dataManager.formatHUF(Math.round(value));
            const y = bar.y - 8;
            
            ctx.font = '600 12px \'Segoe UI\', sans-serif';
            ctx.fillStyle = textColor;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'bottom';
            ctx.fillText(formatted, bar.x, y);
        });
        
        ctx.restore();
    }

    /**
     * Update all charts with new data
     */
    updateAllCharts(data) {
        // Update trend chart
        const trendChart = this.charts.get('trendChart');
        if (trendChart) {
            trendChart.data.labels = data.map(d => d.dateString);
            this.setTrendMetric(this.currentTrendMetric);
        }

        // Update yearly chart
        const yearlyChart = this.charts.get('yearlyChart');
        if (yearlyChart) {
            const yearlyData = this.dataManager.calculateYearlyAverages();
            const years = Object.keys(yearlyData);
            
            yearlyChart.data.labels = years;
            yearlyChart.data.datasets[0].data = years.map(year => yearlyData[year].avgWaste);
            yearlyChart.data.datasets[1].data = years.map(year => yearlyData[year].avgMargin);
            yearlyChart.data.datasets[2].data = years.map(year => yearlyData[year].avgWastePercent);
            yearlyChart.update();
        }

        // Update mini charts
        this.updateMiniCharts(data);
    }

    /**
     * Update mini charts
     */
    updateMiniCharts(data) {
        const wasteData = data.map(d => d.wasteHUF);
        const marginData = data.map(d => d.marginPercent);
        const wastePctData = data.map(d => d.wastePercent);

        this.updateMiniChart('miniWasteChart', wasteData, 'bar');
        this.updateMiniChart('miniMarginChart', marginData, 'line');
        this.updateMiniChart('miniWastePctChart', wastePctData, 'line');
    }

    /**
     * Update individual mini chart
     */
    updateMiniChart(chartId, data, type) {
        let chart = this.charts.get(chartId);
        if (chart) {
            chart.destroy();
        }
        chart = this.createMiniChart(chartId, data, type);
    }

    /**
     * Update chart colors based on current theme
     */
    updateChartColors() {
        const colors = this.getCurrentColors();
        
        this.charts.forEach((chart, id) => {
            if (id === 'trendChart') {
                const gradient = this.createGradient(chart.ctx, colors.accent.start, colors.accent.end);
                chart.data.datasets[0].borderColor = colors.accent.start;
                chart.data.datasets[0].pointBackgroundColor = colors.accent.start;
                chart.data.datasets[0].backgroundColor = gradient;
                
                if (chart.data.datasets[1]) {
                    chart.data.datasets[1].borderColor = colors.accent.alt;
                }
            } else if (id === 'yearlyChart') {
                chart.data.datasets[0].backgroundColor = [colors.accent.start, colors.accent.end, colors.accent.alt];
                chart.data.datasets[1].borderColor = colors.accent.end;
                chart.data.datasets[1].backgroundColor = colors.accent.end + '33';
                chart.data.datasets[2].borderColor = colors.accent.alt;
                chart.data.datasets[2].backgroundColor = colors.accent.alt + '33';
            } else if (id.startsWith('mini')) {
                const dataset = chart.data.datasets[0];
                dataset.borderColor = colors.accent.start;
                dataset.backgroundColor = chart.config.type === 'bar' ? 
                    colors.accent.start + '55' : 
                    colors.accent.start + '33';
            }
            
            chart.update('none');
        });
    }

    /**
     * Get current theme colors
     */
    getCurrentColors() {
        const root = document.documentElement;
        return {
            accent: {
                start: getComputedStyle(root).getPropertyValue('--accent-start').trim(),
                end: getComputedStyle(root).getPropertyValue('--accent-end').trim(),
                alt: getComputedStyle(root).getPropertyValue('--accent-alt').trim()
            }
        };
    }

    /**
     * Create gradient for chart backgrounds
     */
    createGradient(ctx, startColor, endColor) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, startColor + 'EE');
        gradient.addColorStop(1, endColor + '22');
        return gradient;
    }

    /**
     * Get min/max values for chart scaling
     */
    getMinMax(values) {
        if (!values.length) return { min: 0, max: 1 };
        
        const min = Math.min(...values);
        const max = Math.max(...values);
        
        if (min === max) {
            return { min, max: min + 1 };
        }
        
        return { min, max };
    }

    /**
     * Format tooltip value
     */
    formatTooltipValue(value) {
        if (typeof value === 'number') {
            return value >= 1000 ? 
                this.dataManager.formatHUF(value) : 
                value.toFixed(2);
        }
        return value.toString();
    }

    /**
     * Destroy all charts
     */
    destroyAllCharts() {
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
        this.clearPinnedTooltips();
    }

    /**
     * Get chart instance by ID
     */
    getChart(chartId) {
        return this.charts.get(chartId);
    }
}

export { ChartManager };
