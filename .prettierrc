{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "overrides": [{"files": "*.css", "options": {"singleQuote": false}}, {"files": "*.html", "options": {"printWidth": 120, "htmlWhitespaceSensitivity": "ignore"}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}, {"files": "*.json", "options": {"printWidth": 120}}]}