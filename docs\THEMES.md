# 🎨 Theming Guide

Complete guide to the advanced theming system in Financial Dashboard v2.0.

## 🌈 Overview

The theming system is built on CSS custom properties (variables) and provides:
- **15+ predefined themes** across 5 categories
- **Real-time color customization**
- **Theme persistence** across sessions
- **Accessibility compliance** (AA/AAA ratings)
- **Facts mode** for nutrition label-style formatting

## 🎯 Theme Categories

### 💼 Professional Themes
Business and corporate-focused themes with conservative color palettes.

#### Corporate Blue
- **ID**: `corporate`
- **Colors**: Professional blues with subtle gradients
- **Use Case**: Corporate dashboards, business presentations
- **Accessibility**: AAA

#### Executive Gray
- **ID**: `executive`
- **Colors**: Sophisticated grayscale with minimal accents
- **Use Case**: Executive reports, formal presentations
- **Accessibility**: AAA

#### Financial Green
- **ID**: `financial`
- **Colors**: Finance-focused greens with gold accents
- **Use Case**: Financial analysis, banking applications
- **Accessibility**: AA

### 🚀 Modern Themes
Contemporary and futuristic themes with bold color combinations.

#### Aurora Borealis
- **ID**: `aurora`
- **Colors**: Deep blues with bright yellow accents
- **Use Case**: Creative dashboards, modern interfaces
- **Accessibility**: AA

#### Cyberpunk Neon
- **ID**: `cyberpunk`
- **Colors**: Dark backgrounds with neon highlights
- **Use Case**: Tech-focused applications, gaming interfaces
- **Accessibility**: AA

#### Synthwave Retro
- **ID**: `synthwave`
- **Colors**: 80s-inspired purples and pinks
- **Use Case**: Creative projects, retro-themed applications
- **Accessibility**: AA

### 🌿 Nature Themes
Earth-inspired themes with natural color palettes.

#### Deep Forest
- **ID**: `forest`
- **Colors**: Forest greens with earth tones
- **Use Case**: Environmental dashboards, outdoor applications
- **Accessibility**: AAA

#### Ocean Depths
- **ID**: `ocean`
- **Colors**: Deep ocean blues with aqua highlights
- **Use Case**: Marine applications, calming interfaces
- **Accessibility**: AA

#### Sunset Calm
- **ID**: `sunset`
- **Colors**: Warm sunset colors with purple undertones
- **Use Case**: Wellness applications, evening interfaces
- **Accessibility**: AA

### ♿ Accessibility Themes
High contrast themes designed for accessibility compliance.

#### Monochrome
- **ID**: `monochrome`
- **Colors**: Pure black and white
- **Use Case**: Maximum accessibility, print-friendly
- **Accessibility**: AAA

#### High Contrast
- **ID**: `highContrast`
- **Colors**: High contrast colors with clear differentiation
- **Use Case**: Visual impairment support, accessibility compliance
- **Accessibility**: AAA

### 🍂 Seasonal Themes
Season-inspired themes that change throughout the year.

#### Spring Bloom
- **ID**: `spring`
- **Colors**: Fresh greens with pink accents
- **Use Case**: Spring campaigns, fresh interfaces
- **Accessibility**: AA

#### Autumn Leaves
- **ID**: `autumn`
- **Colors**: Warm oranges and browns
- **Use Case**: Autumn campaigns, cozy interfaces
- **Accessibility**: AA

#### Winter Frost
- **ID**: `winter`
- **Colors**: Cool blues with ice-like highlights
- **Use Case**: Winter campaigns, cool interfaces
- **Accessibility**: AA

## 🔧 Custom Theme Creation

### Using the Theme Manager
```javascript
import { themeManager } from './js/modules/theme-manager.js';

const customTheme = themeManager.createCustomTheme('My Custom Theme', {
    accentStart: '#ff6b35',    // Primary accent color
    accentEnd: '#f7931e',      // Secondary accent color
    accentAlt: '#ffb347',      // Alternative accent color
    accentNeutral: '#95a5a6',  // Neutral accent color
    accentWarn: '#e74c3c',     // Warning color
    bgStart: '#2c3e50',        // Background gradient start
    bgEnd: '#34495e',          // Background gradient end
    cardBg: 'rgba(52, 73, 94, 0.85)', // Card background
    positive: '#27ae60',       // Positive indicator color
    negative: '#e74c3c'        // Negative indicator color
});

// Apply the custom theme
themeManager.switchTheme(customTheme.id);
```

### Direct CSS Variable Manipulation
```javascript
// Update CSS variables directly
document.documentElement.style.setProperty('--accent-start', '#ff6b35');
document.documentElement.style.setProperty('--accent-end', '#f7931e');
```

### Color Palette Object Structure
```javascript
const paletteDefinition = {
    id: 'unique-theme-id',
    name: 'Human Readable Name',
    category: 'professional', // professional, modern, nature, accessibility, seasonal
    description: 'Theme description for tooltips',
    accessibility: 'AAA', // AAA, AA, A
    vars: {
        '--accent-start': '#color-value',
        '--accent-end': '#color-value',
        '--accent-alt': '#color-value',
        '--accent-neutral': '#color-value',
        '--accent-warn': '#color-value',
        '--bg-gradient-start': '#color-value',
        '--bg-gradient-end': '#color-value',
        '--card-bg': 'rgba(r, g, b, alpha)',
        '--positive': '#color-value',
        '--negative': '#color-value'
    }
};
```

## 🎨 CSS Custom Properties

### Core Color System
```css
:root {
    /* Primary Brand Colors */
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    /* ... full color scale ... */
    --color-primary-950: #082f49;

    /* Semantic Colors */
    --color-success-500: #22c55e;
    --color-warning-500: #f59e0b;
    --color-error-500: #ef4444;
    --color-info-500: #3b82f6;
}
```

### Theme Variables
```css
:root {
    /* Background System */
    --bg-gradient-start: var(--color-neutral-900);
    --bg-gradient-end: var(--color-neutral-800);
    --bg-primary: var(--color-neutral-900);
    --bg-secondary: var(--color-neutral-800);

    /* Accent System */
    --accent-start: var(--color-primary-500);
    --accent-end: var(--color-secondary-500);
    --accent-alt: var(--color-accent-500);

    /* Card System */
    --card-bg: rgba(30, 41, 59, 0.85);
    --card-border: rgba(148, 163, 184, 0.15);
    --card-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.4);

    /* Text System */
    --text-color: var(--color-neutral-200);
    --text-dim: var(--color-neutral-400);
    --text-muted: var(--color-neutral-500);
}
```

### Design Tokens
```css
:root {
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-base: 0.25rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}
```

## 🌓 Light/Dark Mode

### Mode Switching
```javascript
// Toggle between light and dark mode
const newMode = themeManager.toggleMode();

// Check current mode
const currentMode = themeManager.getCurrentState().mode;
```

### Light Theme Overrides
```css
.light-theme {
    --bg-gradient-start: #f5f7fa;
    --bg-gradient-end: #e4ecf7;
    --card-bg: rgba(255, 255, 255, 0.9);
    --text-color: #1e293b;
    --text-dim: #64748b;
}
```

### System Preference Detection
```javascript
// Automatically detect system preference
if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
    themeManager.currentMode = 'dark';
} else if (window.matchMedia('(prefers-color-scheme: light)').matches) {
    themeManager.currentMode = 'light';
}
```

## 📊 Facts Mode

Special formatting mode that mimics nutrition label styling.

### Enabling Facts Mode
```javascript
// Toggle facts mode
const factsMode = themeManager.toggleFactsMode();

// Toggle facts color mode
const factsColorMode = themeManager.toggleFactsColorMode();
```

### Facts Mode Styling
```css
.facts-mode .data-table {
    background: #ffffff;
    color: #000000;
    border: 2px solid #000000;
}

.facts-mode .data-table th {
    border-bottom: 6px solid #000000;
    font-weight: bold;
}

.facts-mode .percentage {
    background: #000000;
    color: #ffffff;
    border-radius: 0;
}
```

### Facts Color Mode
Adds color coding to facts mode for better data visualization:
- **High waste**: Red indicators
- **Medium waste**: Gray indicators  
- **Low waste**: Green indicators

## 🎯 Best Practices

### Theme Selection Guidelines

#### For Business Applications
- Use **Professional** themes (Corporate, Executive, Financial)
- Ensure **AAA accessibility** compliance
- Test with colorblind users

#### For Creative Applications
- Use **Modern** or **Nature** themes
- Consider **seasonal** themes for time-sensitive content
- Balance aesthetics with usability

#### For Accessibility
- Always use **High Contrast** or **Monochrome** themes when required
- Test with screen readers
- Ensure sufficient color contrast ratios

### Performance Considerations

#### CSS Variable Updates
```javascript
// Batch CSS variable updates for better performance
const updates = {
    '--accent-start': '#ff6b35',
    '--accent-end': '#f7931e',
    '--accent-alt': '#ffb347'
};

Object.entries(updates).forEach(([property, value]) => {
    document.documentElement.style.setProperty(property, value);
});
```

#### Theme Persistence
```javascript
// Themes are automatically saved to localStorage
// Manual save/load if needed
const settings = themeManager.exportSettings();
localStorage.setItem('themeSettings', JSON.stringify(settings));

const savedSettings = JSON.parse(localStorage.getItem('themeSettings'));
themeManager.importSettings(savedSettings);
```

## 🔍 Debugging Themes

### Inspect Current Theme
```javascript
// Get current theme state
const state = themeManager.getCurrentState();
console.log('Current theme:', state);

// Get current palette
const palette = themeManager.getCurrentPalette();
console.log('Current palette:', palette);
```

### CSS Variable Inspection
```javascript
// Get computed CSS variable value
const accentColor = getComputedStyle(document.documentElement)
    .getPropertyValue('--accent-start');
console.log('Accent color:', accentColor);
```

### Theme Validation
```javascript
// Validate theme object
function validateTheme(theme) {
    const requiredProps = ['id', 'name', 'category', 'vars'];
    return requiredProps.every(prop => theme.hasOwnProperty(prop));
}
```

## 🚀 Advanced Customization

### Dynamic Theme Generation
```javascript
// Generate theme from base colors
function generateThemeFromBase(baseColor) {
    // Use color manipulation library to generate palette
    const palette = generatePalette(baseColor);
    
    return themeManager.createCustomTheme('Generated Theme', {
        accentStart: palette.primary,
        accentEnd: palette.secondary,
        accentAlt: palette.tertiary,
        // ... other colors
    });
}
```

### Theme Animation
```css
/* Smooth theme transitions */
:root {
    transition: 
        --accent-start 0.3s ease,
        --accent-end 0.3s ease,
        --bg-gradient-start 0.5s ease,
        --bg-gradient-end 0.5s ease;
}
```

### Conditional Theme Loading
```javascript
// Load theme based on time of day
const hour = new Date().getHours();
if (hour >= 6 && hour < 18) {
    themeManager.switchTheme('spring');
} else {
    themeManager.switchTheme('winter');
}
```
