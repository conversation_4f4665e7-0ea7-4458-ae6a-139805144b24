/* ===================================================================
   COMPONENT STYLES
   Reusable UI components and patterns
   =================================================================== */

/* ===============================
   HEADER COMPONENT
   =============================== */

.header {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0));
    backdrop-filter: blur(22px) saturate(140%);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-3xl);
    padding: var(--space-2xl) clamp(var(--space-lg), 4vw, var(--space-3xl));
    margin-bottom: var(--space-xl);
    box-shadow: var(--card-shadow);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: "";
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08), transparent 60%);
    pointer-events: none;
}

.header p {
    color: var(--text-dim);
    font-size: var(--font-size-lg);
    margin-bottom: 0;
}

/* ===============================
   CARD COMPONENT
   =============================== */

.card {
    background: linear-gradient(155deg, var(--card-bg), rgba(255, 255, 255, 0.02));
    backdrop-filter: blur(18px) saturate(160%);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-xl) var(--space-2xl);
    box-shadow: var(--card-shadow);
    transition: transform var(--duration-normal) var(--ease-out),
                box-shadow var(--duration-normal) var(--ease-out),
                border-color var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: "";
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06), transparent 65%);
    opacity: 0;
    transition: opacity var(--duration-slow) var(--ease-out);
    pointer-events: none;
}

.card:hover {
    transform: translateY(-6px) scale(1.01);
    box-shadow: 0 32px 70px -12px rgba(0, 0, 0, 0.55);
    border-color: rgba(255, 255, 255, 0.18);
}

.card:hover::before {
    opacity: 1;
}

/* ===============================
   ICON COMPONENT
   =============================== */

.icon {
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, var(--accent-start), var(--accent-end));
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 15px;
    box-shadow: 0 4px 10px -2px rgba(0, 0, 0, 0.4);
    flex-shrink: 0;
}

/* ===============================
   BUTTON COMPONENTS
   =============================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-full);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    letter-spacing: var(--letter-spacing-wide);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--button-primary);
    color: white;
    border-color: transparent;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px -6px rgba(0, 0, 0, 0.55);
}

.btn-secondary {
    background: var(--button-secondary);
    color: var(--text-color);
    border-color: var(--card-border);
}

.btn-secondary:hover {
    background: linear-gradient(120deg, var(--accent-start), var(--accent-end));
    color: white;
    border-color: transparent;
}

.btn-ghost {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--card-border);
    color: var(--text-dim);
}

.btn-ghost:hover {
    background: rgba(255, 255, 255, 0.15);
}

.btn-small {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
    letter-spacing: var(--letter-spacing-wider);
    text-transform: uppercase;
}

.btn-large {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-lg);
}

/* ===============================
   STAT CARD COMPONENT
   =============================== */

.stat-card {
    background: linear-gradient(150deg, var(--card-bg), rgba(255, 255, 255, 0.03));
    backdrop-filter: blur(16px) saturate(160%);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-lg) var(--space-lg) var(--space-lg);
    text-align: center;
    box-shadow: 0 12px 40px -12px rgba(0, 0, 0, 0.5);
    transition: transform var(--duration-normal) var(--ease-out),
                border-color var(--duration-normal) var(--ease-out);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: "";
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 80% 25%, rgba(255, 255, 255, 0.07), transparent 62%);
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out);
}

.stat-card:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 255, 255, 0.18);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-value {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-xs);
    background: linear-gradient(120deg, var(--accent-start), var(--accent-end));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: var(--letter-spacing-wide);
}

.stat-label {
    color: var(--text-dim);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-widest);
}

/* ===============================
   PERCENTAGE PILL COMPONENT
   =============================== */

.percentage {
    background: var(--pill-bg);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-wide);
    color: var(--accent-start);
    display: inline-block;
}

/* ===============================
   BADGE COMPONENT
   =============================== */

.badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-wide);
}

.badge-success {
    background: rgba(34, 197, 94, 0.15);
    color: var(--color-success-400);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.15);
    color: var(--color-warning-400);
}

.badge-error {
    background: rgba(239, 68, 68, 0.15);
    color: var(--color-error-400);
}

.badge-info {
    background: rgba(59, 130, 246, 0.15);
    color: var(--color-info-400);
}

/* ===============================
   GRID LAYOUTS
   =============================== */

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-2xl);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
}

.layout-row {
    display: flex;
    gap: var(--space-2xl);
    margin-bottom: var(--space-3xl);
    align-items: stretch;
}

.two-col-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--space-2xl);
    margin-top: var(--space-3xl);
}

/* ===============================
   CHART CONTAINER
   =============================== */

.chart-container {
    position: relative;
    height: 400px;
    margin-top: var(--space-lg);
}

.chart-header-row {
    display: flex;
    gap: var(--space-md);
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: var(--space-lg);
}

.chart-controls {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
    flex-wrap: wrap;
    margin-left: auto;
}

/* ===============================
   DIVIDER COMPONENT
   =============================== */

.divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--card-border), transparent);
    margin: var(--space-lg) 0;
}

/* ===============================
   LOADING STATES
   =============================== */

.skeleton {
    background: linear-gradient(90deg, var(--card-bg) 25%, rgba(255, 255, 255, 0.1) 50%, var(--card-bg) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--radius-base);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ===============================
   PANEL COMPONENTS
   =============================== */

.panel {
    position: fixed;
    top: 70px;
    right: var(--space-lg);
    width: 320px;
    max-height: calc(100vh - 90px);
    overflow-y: auto;
    background: linear-gradient(150deg, rgba(15, 23, 42, 0.92), rgba(30, 41, 59, 0.92));
    backdrop-filter: blur(20px) saturate(160%);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-lg) var(--space-lg) var(--space-xl);
    box-shadow: 0 20px 60px -15px rgba(0, 0, 0, 0.65);
    z-index: var(--z-modal);
    color: var(--text-color);
    display: none;
    transform: translateY(-8px);
    opacity: 0;
    transition: all var(--duration-normal) var(--ease-out);
}

.panel.open {
    display: block;
    animation: panel-enter var(--duration-slow) var(--ease-out);
}

@keyframes panel-enter {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.panel-toggle {
    position: fixed;
    top: var(--space-lg);
    right: var(--space-lg);
    z-index: calc(var(--z-modal) + 10);
    background: linear-gradient(135deg, var(--accent-start), var(--accent-end));
    color: white;
    border: none;
    padding: var(--space-sm) var(--space-md);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-xl);
    cursor: pointer;
    box-shadow: 0 12px 32px -8px rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    letter-spacing: var(--letter-spacing-wide);
    transition: transform var(--duration-normal) var(--ease-out);
}

.panel-toggle:hover {
    transform: translateY(-3px);
}

.panel-close {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-sm);
    background: rgba(255, 255, 255, 0.07);
    border: 1px solid var(--card-border);
    width: 28px;
    height: 28px;
    border-radius: 50%;
    cursor: pointer;
    color: var(--text-dim);
    font-weight: var(--font-weight-bold);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background var(--duration-fast) var(--ease-out);
}

.panel-close:hover {
    background: rgba(255, 255, 255, 0.15);
}

/* ===============================
   FORM COMPONENTS
   =============================== */

.form-group {
    margin-bottom: var(--space-md);
}

.form-label {
    display: block;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-wider);
    text-transform: uppercase;
    color: var(--text-dim);
    margin-bottom: var(--space-xs);
}

.form-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}

.form-row .form-label {
    flex: 1;
    margin-bottom: 0;
}

.form-actions {
    display: flex;
    gap: var(--space-sm);
    margin: var(--space-xs) 0 var(--space-md);
}

.form-actions .btn {
    flex: 1;
}

/* ===============================
   TOGGLE COMPONENTS
   =============================== */

.toggle-group {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-full);
    padding: 2px;
}

.toggle-btn {
    background: transparent;
    border: none;
    padding: var(--space-xs) var(--space-md);
    font-size: var(--font-size-xs);
    letter-spacing: var(--letter-spacing-wide);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-out);
}

.toggle-btn.active {
    background: linear-gradient(90deg, var(--accent-start), var(--accent-end));
    color: white;
}

/* ===============================
   MINI CHART COMPONENTS
   =============================== */

.mini-card {
    background: linear-gradient(150deg, var(--card-bg), rgba(255, 255, 255, 0.03));
    backdrop-filter: blur(14px) saturate(160%);
    border: 1px solid var(--card-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-md) var(--space-lg) var(--space-md);
    position: relative;
    box-shadow: 0 10px 28px -10px rgba(0, 0, 0, 0.55);
    display: flex;
    flex-direction: column;
}

.mini-card h4 {
    margin: 0 0 var(--space-sm);
    font-size: var(--font-size-xs);
    letter-spacing: var(--letter-spacing-wider);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    color: var(--text-dim);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.mini-card canvas {
    width: 100% !important;
    height: 90px !important;
}

.value-badge {
    position: absolute;
    top: var(--space-sm);
    right: var(--space-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    background: var(--pill-bg);
    color: var(--accent-start);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-full);
    letter-spacing: var(--letter-spacing-wide);
}
