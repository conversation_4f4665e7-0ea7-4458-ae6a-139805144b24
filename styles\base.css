/* ===================================================================
   BASE STYLES
   Reset, typography, and foundational styles
   =================================================================== */

/* ===============================
   RESET & NORMALIZE
   =============================== */

*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-color);
    background: radial-gradient(circle at 25% 20%, var(--bg-gradient-start), var(--bg-gradient-end));
    min-height: 100vh;
    padding: var(--space-lg) var(--space-lg) 80px;
    transition: background var(--duration-slow) var(--ease-out), 
                color var(--duration-normal) var(--ease-out);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===============================
   TYPOGRAPHY
   =============================== */

h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    margin-bottom: var(--space-md);
}

h1 {
    font-size: clamp(2rem, 5vw, 2.8rem);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(90deg, var(--accent-start), var(--accent-end));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: var(--letter-spacing-wide);
}

h2 {
    font-size: var(--font-size-2xl);
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    letter-spacing: var(--letter-spacing-wide);
}

h3 {
    font-size: var(--font-size-xl);
    color: var(--text-dim);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wider);
}

h4 {
    font-size: var(--font-size-lg);
    color: var(--text-dim);
    text-transform: uppercase;
    letter-spacing: var(--letter-spacing-wider);
}

p {
    margin-bottom: var(--space-md);
    color: var(--text-dim);
}

/* ===============================
   LINKS & BUTTONS
   =============================== */

a {
    color: var(--accent-start);
    text-decoration: none;
    transition: color var(--duration-fast) var(--ease-out);
}

a:hover {
    color: var(--accent-end);
}

button {
    font-family: inherit;
    font-size: inherit;
    cursor: pointer;
    border: none;
    background: none;
    transition: all var(--duration-normal) var(--ease-out);
}

button:focus {
    outline: 2px solid var(--accent-start);
    outline-offset: 2px;
}

/* ===============================
   FORM ELEMENTS
   =============================== */

input,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    border: 1px solid var(--card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-sm) var(--space-md);
    background: var(--card-bg);
    color: var(--text-color);
    transition: border-color var(--duration-fast) var(--ease-out),
                box-shadow var(--duration-fast) var(--ease-out);
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--accent-start);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

input[type="color"] {
    width: 46px;
    height: 36px;
    border: 1px solid var(--card-border);
    border-radius: var(--radius-lg);
    background: transparent;
    cursor: pointer;
    padding: 0;
}

/* ===============================
   TABLES
   =============================== */

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    background: var(--card-bg-solid);
    border: 1px solid var(--card-border);
}

th,
td {
    padding: var(--space-md);
    text-align: left;
    border-bottom: 1px solid rgba(148, 163, 184, 0.12);
    transition: background-color var(--duration-normal) var(--ease-out),
                color var(--duration-normal) var(--ease-out);
    vertical-align: middle;
}

th {
    background: linear-gradient(90deg, var(--accent-start), var(--accent-end));
    color: white;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    letter-spacing: var(--letter-spacing-wide);
    white-space: nowrap;
}

tbody tr:hover {
    background-color: var(--table-row-hover);
}

tbody tr:last-child td {
    border-bottom: none;
}

/* ===============================
   UTILITY CLASSES
   =============================== */

.container {
    max-width: 1400px;
    margin: 0 auto;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.font-mono {
    font-family: var(--font-family-mono);
    font-variant-numeric: tabular-nums;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    border-top-color: var(--accent-start);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===============================
   SCROLLBAR STYLING
   =============================== */

::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.03);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(var(--accent-start), var(--accent-end));
    border-radius: 20px;
    border: 2px solid rgba(0, 0, 0, 0.4);
}

::selection {
    background: var(--accent-start);
    color: white;
}

/* ===============================
   FOCUS MANAGEMENT
   =============================== */

.focus-trap {
    position: relative;
}

.focus-trap:focus-within {
    outline: 2px solid var(--accent-start);
    outline-offset: 2px;
}

/* ===============================
   PRINT STYLES
   =============================== */

@media print {
    body {
        background: white !important;
        color: black !important;
        padding: 0;
    }
    
    .no-print {
        display: none !important;
    }
    
    .print-break-before {
        page-break-before: always;
    }
    
    .print-break-after {
        page-break-after: always;
    }
}
