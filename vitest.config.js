import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    // Test environment
    environment: 'jsdom',
    
    // Global setup
    globals: true,
    setupFiles: ['./tests/setup.js'],
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'dist/',
        'coverage/',
        'tests/',
        '**/*.config.js',
        '**/*.test.js',
        '**/*.spec.js'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Test files
    include: ['tests/**/*.{test,spec}.js'],
    exclude: ['node_modules/', 'dist/'],
    
    // Test timeout
    testTimeout: 10000,
    
    // Watch options
    watch: false,
    
    // Reporter
    reporter: ['verbose', 'html'],
    outputFile: {
      html: './reports/test-results.html'
    }
  },
  
  // Define global constants for tests
  define: {
    __APP_VERSION__: JSON.stringify('2.0.0-test'),
    __BUILD_DATE__: JSON.stringify(new Date().toISOString()),
    __DEV__: JSON.stringify(true)
  }
});
