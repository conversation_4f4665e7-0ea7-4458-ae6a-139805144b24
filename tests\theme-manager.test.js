/**
 * Tests for ThemeManager module
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ThemeManager } from '../js/modules/theme-manager.js';

describe('ThemeManager', () => {
  let themeManager;

  beforeEach(() => {
    // Reset DOM
    document.body.className = '';
    document.documentElement.style.cssText = '';
    
    // Create new instance for each test
    themeManager = new ThemeManager();
  });

  describe('Initialization', () => {
    it('should initialize with default values', () => {
      expect(themeManager.currentTheme).toBe('corporate');
      expect(themeManager.currentMode).toBe('dark');
      expect(themeManager.factsMode).toBe(false);
      expect(themeManager.factsColorMode).toBe(false);
    });

    it('should load stored settings from localStorage', () => {
      localStorage.getItem.mockImplementation((key) => {
        if (key === 'dashThemeV2') return 'aurora';
        if (key === 'dashModeV2') return 'light';
        if (key === 'dashFactsV2') return 'true';
        return null;
      });

      const tm = new ThemeManager();
      expect(tm.currentTheme).toBe('aurora');
      expect(tm.currentMode).toBe('light');
      expect(tm.factsMode).toBe(true);
    });
  });

  describe('Theme Switching', () => {
    it('should switch to a valid theme', () => {
      const result = themeManager.switchTheme('aurora');
      expect(result).toBe(true);
      expect(themeManager.currentTheme).toBe('aurora');
    });

    it('should not switch to an invalid theme', () => {
      const result = themeManager.switchTheme('nonexistent');
      expect(result).toBe(false);
      expect(themeManager.currentTheme).toBe('corporate');
    });

    it('should apply theme classes to body', () => {
      themeManager.switchTheme('aurora');
      expect(document.body.classList.contains('theme-aurora')).toBe(true);
    });

    it('should save theme to localStorage', () => {
      themeManager.switchTheme('aurora');
      expect(localStorage.setItem).toHaveBeenCalledWith('dashThemeV2', 'aurora');
    });
  });

  describe('Mode Toggle', () => {
    it('should toggle from dark to light mode', () => {
      const newMode = themeManager.toggleMode();
      expect(newMode).toBe('light');
      expect(themeManager.currentMode).toBe('light');
      expect(document.body.classList.contains('light-theme')).toBe(true);
    });

    it('should toggle from light to dark mode', () => {
      themeManager.currentMode = 'light';
      const newMode = themeManager.toggleMode();
      expect(newMode).toBe('dark');
      expect(themeManager.currentMode).toBe('dark');
      expect(document.body.classList.contains('light-theme')).toBe(false);
    });
  });

  describe('Facts Mode', () => {
    it('should toggle facts mode on', () => {
      const factsMode = themeManager.toggleFactsMode();
      expect(factsMode).toBe(true);
      expect(themeManager.factsMode).toBe(true);
      expect(document.body.classList.contains('facts-mode')).toBe(true);
    });

    it('should toggle facts mode off', () => {
      themeManager.factsMode = true;
      const factsMode = themeManager.toggleFactsMode();
      expect(factsMode).toBe(false);
      expect(themeManager.factsMode).toBe(false);
      expect(document.body.classList.contains('facts-mode')).toBe(false);
    });

    it('should toggle facts color mode', () => {
      const factsColorMode = themeManager.toggleFactsColorMode();
      expect(factsColorMode).toBe(true);
      expect(themeManager.factsColorMode).toBe(true);
      expect(document.body.classList.contains('facts-color')).toBe(true);
    });
  });

  describe('Custom Themes', () => {
    it('should create a custom theme', () => {
      const colors = {
        accentStart: '#ff0000',
        accentEnd: '#00ff00',
        accentAlt: '#0000ff'
      };

      const customTheme = themeManager.createCustomTheme('Test Theme', colors);
      
      expect(customTheme.name).toBe('Test Theme');
      expect(customTheme.category).toBe('custom');
      expect(customTheme.vars['--accent-start']).toBe('#ff0000');
      expect(themeManager.customThemes.has(customTheme.id)).toBe(true);
    });

    it('should delete a custom theme', () => {
      const customTheme = themeManager.createCustomTheme('Test Theme', {});
      const success = themeManager.deleteCustomTheme(customTheme.id);
      
      expect(success).toBe(true);
      expect(themeManager.customThemes.has(customTheme.id)).toBe(false);
    });

    it('should switch to default when deleting current custom theme', () => {
      const customTheme = themeManager.createCustomTheme('Test Theme', {});
      themeManager.switchTheme(customTheme.id);
      themeManager.deleteCustomTheme(customTheme.id);
      
      expect(themeManager.currentTheme).toBe('corporate');
    });
  });

  describe('Theme Retrieval', () => {
    it('should get all themes including custom ones', () => {
      const customTheme = themeManager.createCustomTheme('Test Theme', {});
      const allThemes = themeManager.getAllThemes();
      
      expect(allThemes.length).toBeGreaterThan(0);
      expect(allThemes.some(theme => theme.id === customTheme.id)).toBe(true);
    });

    it('should get themes by category', () => {
      const professionalThemes = themeManager.getThemesByCategory('professional');
      expect(professionalThemes.length).toBeGreaterThan(0);
      expect(professionalThemes.every(theme => theme.category === 'professional')).toBe(true);
    });

    it('should get current state', () => {
      const state = themeManager.getCurrentState();
      expect(state).toHaveProperty('theme');
      expect(state).toHaveProperty('mode');
      expect(state).toHaveProperty('factsMode');
      expect(state).toHaveProperty('factsColorMode');
      expect(state).toHaveProperty('palette');
    });
  });

  describe('Reset Functionality', () => {
    it('should reset to default theme', () => {
      themeManager.switchTheme('aurora');
      themeManager.toggleMode();
      themeManager.toggleFactsMode();
      
      themeManager.resetToDefault();
      
      expect(themeManager.currentTheme).toBe('corporate');
      expect(themeManager.currentMode).toBe('dark');
      expect(themeManager.factsMode).toBe(false);
      expect(themeManager.factsColorMode).toBe(false);
    });
  });

  describe('Settings Import/Export', () => {
    it('should export settings', () => {
      themeManager.switchTheme('aurora');
      themeManager.toggleMode();
      
      const settings = themeManager.exportSettings();
      expect(settings.theme).toBe('aurora');
      expect(settings.mode).toBe('light');
    });

    it('should import settings', () => {
      const settings = {
        theme: 'forest',
        mode: 'light',
        factsMode: true,
        factsColorMode: true
      };

      const success = themeManager.importSettings(settings);
      expect(success).toBe(true);
      expect(themeManager.currentTheme).toBe('forest');
      expect(themeManager.currentMode).toBe('light');
      expect(themeManager.factsMode).toBe(true);
      expect(themeManager.factsColorMode).toBe(true);
    });

    it('should handle invalid import settings', () => {
      const success = themeManager.importSettings(null);
      expect(success).toBe(false);
    });
  });

  describe('Event Handling', () => {
    it('should dispatch theme change event', () => {
      const eventSpy = vi.spyOn(document, 'dispatchEvent');
      
      themeManager.switchTheme('aurora');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'themechange',
          detail: expect.objectContaining({
            theme: 'aurora'
          })
        })
      );
    });

    it('should handle storage events from other tabs', () => {
      const storageEvent = new StorageEvent('storage', {
        key: 'dashThemeV2',
        newValue: 'forest'
      });

      // Mock the stored value
      localStorage.getItem.mockReturnValue('forest');
      
      window.dispatchEvent(storageEvent);
      
      // Should reload settings and apply theme
      expect(themeManager.currentTheme).toBe('forest');
    });
  });

  describe('System Preferences', () => {
    it('should detect dark mode preference', () => {
      // Mock matchMedia to return dark mode preference
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: dark)',
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      }));

      // Clear localStorage to test system preference detection
      localStorage.getItem.mockReturnValue(null);
      
      const tm = new ThemeManager();
      expect(tm.currentMode).toBe('dark');
    });

    it('should detect light mode preference', () => {
      // Mock matchMedia to return light mode preference
      window.matchMedia = vi.fn().mockImplementation((query) => ({
        matches: query === '(prefers-color-scheme: light)',
        addEventListener: vi.fn(),
        removeEventListener: vi.fn()
      }));

      // Clear localStorage to test system preference detection
      localStorage.getItem.mockReturnValue(null);
      
      const tm = new ThemeManager();
      expect(tm.currentMode).toBe('light');
    });
  });
});
