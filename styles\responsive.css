/* ===================================================================
   RESPONSIVE DESIGN
   Mobile-first responsive breakpoints and adaptations
   =================================================================== */

/* ===============================
   MOBILE FIRST BASE STYLES
   =============================== */

/* Base styles are mobile-first, so no media query needed for smallest screens */

/* ===============================
   SMALL DEVICES (640px and up)
   =============================== */

@media (min-width: 640px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .layout-row {
        flex-direction: column;
    }
}

/* ===============================
   MEDIUM DEVICES (768px and up)
   =============================== */

@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .layout-row {
        flex-direction: row;
    }
    
    .mini-card canvas {
        height: 100px !important;
    }
    
    .panel {
        width: 350px;
    }
}

/* ===============================
   LARGE DEVICES (1024px and up)
   =============================== */

@media (min-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .layout-row .yearly-card {
        min-height: 480px;
    }
    
    .yearly-card .chart-container {
        min-height: 440px;
    }
    
    .panel {
        width: 380px;
    }
}

/* ===============================
   EXTRA LARGE DEVICES (1280px and up)
   =============================== */

@media (min-width: 1280px) {
    .container {
        max-width: 1400px;
    }
    
    .layout-row .yearly-card {
        min-height: 520px;
    }
    
    .yearly-card .chart-container {
        min-height: 480px;
    }
}

/* ===============================
   MOBILE SPECIFIC OVERRIDES
   =============================== */

@media (max-width: 639px) {
    body {
        padding: var(--space-md) var(--space-md) 80px;
    }
    
    .header {
        padding: var(--space-lg) var(--space-md);
        margin-bottom: var(--space-lg);
    }
    
    .card {
        padding: var(--space-lg) var(--space-md);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
    
    .layout-row {
        flex-direction: column;
        gap: var(--space-lg);
    }
    
    .two-col-row {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }
    
    .panel {
        width: calc(100% - 32px);
        left: var(--space-md);
        right: var(--space-md);
        top: 60px;
    }
    
    .panel-toggle {
        position: fixed;
        bottom: var(--space-lg);
        left: var(--space-lg);
        top: auto;
        right: auto;
    }
    
    .chart-header-row {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }
    
    .chart-controls {
        margin-left: 0;
        justify-content: center;
    }
    
    .mini-card canvas {
        height: 70px !important;
    }
    
    .data-table th,
    .data-table td {
        font-size: var(--font-size-xs);
        padding: var(--space-xs);
    }
    
    .form-row {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-xs);
    }
    
    .form-actions {
        flex-direction: column;
    }
}

/* ===============================
   TABLET SPECIFIC STYLES
   =============================== */

@media (min-width: 640px) and (max-width: 1023px) {
    .layout-row {
        flex-direction: column;
    }
    
    .layout-row .yearly-card {
        min-height: 360px;
    }
    
    .yearly-card .chart-container {
        min-height: 320px;
    }
    
    .two-col-row {
        grid-template-columns: 1fr;
    }
}

/* ===============================
   SMALL HEIGHT SCREENS
   =============================== */

@media (max-height: 900px) {
    .layout-row .yearly-card {
        min-height: 420px;
    }
    
    .yearly-card .chart-container {
        min-height: 380px;
    }
    
    .panel {
        max-height: calc(100vh - 70px);
    }
}

@media (max-height: 760px) {
    .layout-row .yearly-card {
        min-height: 360px;
    }
    
    .yearly-card .chart-container {
        min-height: 320px;
    }
    
    .panel {
        max-height: calc(100vh - 60px);
        top: 60px;
    }
}

/* ===============================
   LANDSCAPE MOBILE
   =============================== */

@media (max-width: 900px) and (orientation: landscape) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .chart-container {
        height: 300px;
    }
    
    .mini-card canvas {
        height: 60px !important;
    }
}

/* ===============================
   HIGH DPI DISPLAYS
   =============================== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===============================
   PRINT STYLES
   =============================== */

@media print {
    .panel-toggle,
    .panel,
    .no-print {
        display: none !important;
    }
    
    .card {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .layout-row {
        flex-direction: column;
    }
    
    .chart-container {
        height: 400px;
    }
    
    .facts-mode.facts-color .bar-wrap {
        display: none;
    }
}

/* ===============================
   REDUCED MOTION
   =============================== */

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .loading {
        animation: none;
    }
    
    .skeleton {
        animation: none;
    }
    
    .pulse {
        animation: none;
    }
}

/* ===============================
   HIGH CONTRAST MODE
   =============================== */

@media (prefers-contrast: high) {
    :root {
        --card-border: rgba(255, 255, 255, 0.3);
        --text-dim: var(--text-color);
    }
    
    .card {
        border-width: 2px;
    }
    
    .btn {
        border-width: 2px;
    }
}

/* ===============================
   DARK MODE PREFERENCE
   =============================== */

@media (prefers-color-scheme: dark) {
    /* Dark mode is default, so no changes needed */
}

@media (prefers-color-scheme: light) {
    /* Users who prefer light mode get light theme by default */
    :root {
        --bg-gradient-start: #f5f7fa;
        --bg-gradient-end: #e4ecf7;
        --accent-start: #667eea;
        --accent-end: #764ba2;
        --accent-alt: #8b5fbf;
        --card-bg: rgba(255, 255, 255, 0.9);
        --card-bg-solid: #ffffff;
        --card-border: rgba(0, 0, 0, 0.06);
        --text-color: #1e293b;
        --text-dim: #64748b;
        --table-row-hover: #f1f5f9;
        --pill-bg: rgba(102, 126, 234, 0.12);
        --card-shadow: 0 20px 40px -10px rgba(31, 41, 55, 0.15);
    }
}
